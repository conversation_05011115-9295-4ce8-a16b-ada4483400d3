from django.urls import path
from . import views

urlpatterns = [
    path('index/', views.index, name='ljjapp_index'),
    path('userlist/', views.user_list, name='ljjapp_userlist'),
    path('adduser/', views.add_user, name='ljjapp_adduser'),
    path('something/', views.something, name='ljjapp_something'),
    path('login/', views.login, name='ljjapp_login'),
    path('newuser/', views.new_user_orm, name='ljjapp_newuser'),
    
    # 用户管理相关路径
    path('user/list/', views.user_list_orm, name='ljjapp_user_list'),
    path('user/add/', views.add_user_orm, name='ljjapp_user_add'),
    path('user/delete/', views.delete_orm, name='ljjapp_user_delete'),
]
