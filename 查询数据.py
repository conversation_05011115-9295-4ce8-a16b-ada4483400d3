import pymysql

#1.链接myql

conn=pymysql.connect(host='127.0.0.1',port=3306,user='root',password='qw069174',charset='utf8',db='unicom')
#接收数据
cursor=conn.cursor(cursor=pymysql.cursors.DictCursor)

sql="select * from admin where id>=2 and id<=5"

cursor.execute(sql)

data_list=cursor.fetchall()

print("查询到的数据条数：",len(data_list))
print(data_list)
print("查询到的数据：")
for data in data_list:
    print (data)






#3.关闭连接

cursor.close()
conn.close()

print(cursor)