<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理系统</title>
    <link rel="stylesheet" href="../static/plugins/bootstrap-3.4.1/css/bootstrap.css">
    <link rel="stylesheet" href="../static/plugins/font-awesome-4.7.0/css/font-awesome.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            padding-top: 20px;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            padding: 30px;
            animation: slideInUp 0.8s ease-out;
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }
        
        .page-title {
            color: #333;
            font-size: 2.5em;
            font-weight: 300;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            animation: fadeInDown 1s ease-out;
        }
        
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .page-subtitle {
            color: #666;
            font-size: 1.1em;
            margin-top: 10px;
            animation: fadeIn 1.2s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .add-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            animation: bounceIn 1.5s ease-out;
        }
        
        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        .add-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
            text-decoration: none;
            color: white;
        }
        
        .add-btn:active {
            transform: translateY(-1px);
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .table {
            margin: 0;
            border-collapse: separate;
            border-spacing: 0;
        }
        
        .table thead th {
            background: linear-gradient(45deg, #4a90e2, #357abd);
            color: white;
            font-weight: 500;
            text-align: center;
            padding: 15px;
            border: none;
            position: relative;
            overflow: hidden;
        }
        
        .table thead th::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .table thead th:hover::before {
            left: 100%;
        }
        
        .table tbody tr {
            transition: all 0.3s ease;
            animation: slideInLeft 0.6s ease-out;
            animation-fill-mode: both;
        }
        
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        .table tbody tr:nth-child(1) { animation-delay: 0.1s; }
        .table tbody tr:nth-child(2) { animation-delay: 0.2s; }
        .table tbody tr:nth-child(3) { animation-delay: 0.3s; }
        .table tbody tr:nth-child(4) { animation-delay: 0.4s; }
        .table tbody tr:nth-child(5) { animation-delay: 0.5s; }
        .table tbody tr:nth-child(n+6) { animation-delay: 0.6s; }
        
        .table tbody tr:hover {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            transform: scale(1.02);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .table tbody td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
            transition: all 0.3s ease;
        }
        
        .table tbody tr:hover td {
            color: #333;
            font-weight: 500;
        }
        
        .delete-btn {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-block;
            box-shadow: 0 3px 10px rgba(220, 53, 69, 0.3);
        }
        
        .delete-btn:hover {
            background: linear-gradient(45deg, #c82333, #bd2130);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
            text-decoration: none;
            color: white;
        }
        
        .delete-btn:active {
            transform: translateY(0);
        }
        
        .user-id {
            background: linear-gradient(45deg, #6c757d, #5a6268);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-weight: bold;
            display: inline-block;
            min-width: 40px;
        }
        
        .user-info {
            position: relative;
        }
        
        .user-info::before {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(45deg, #4a90e2, #357abd);
            transition: width 0.3s ease;
        }
        
        .table tbody tr:hover .user-info::before {
            width: 100%;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 50px;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4a90e2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .stats-bar {
            background: linear-gradient(45deg, #17a2b8, #138496);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            animation: slideInDown 0.8s ease-out 0.2s both;
        }
        
        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .floating-action {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            border-radius: 50%;
            display: block;
            line-height: 60px;
            text-align: center;
            color: white;
            font-size: 24px;
            text-decoration: none;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
            transition: all 0.3s ease;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
            }
            50% {
                box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6);
                transform: scale(1.05);
            }
            100% {
                box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
            }
        }
        
        .floating-action:hover {
            transform: scale(1.1);
            color: white;
            text-decoration: none;
        }
        
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                padding: 20px;
            }
            
            .page-title {
                font-size: 2em;
            }
            
            .table-responsive {
                border-radius: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="main-container">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fa fa-users"></i> 用户管理系统
                </h1>
                <p class="page-subtitle">高效管理您的用户数据</p>
            </div>
            
            <div class="stats-bar">
                <i class="fa fa-database"></i> 
                当前共有 <strong>{{ data_list|length }}</strong> 位用户
            </div>
            
            <div class="text-center">
                <a href="/user/add/" class="add-btn">
                    <i class="fa fa-plus-circle"></i> 添加新用户
                </a>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在加载数据...</p>
            </div>
            
            <div class="table-container" id="tableContainer">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th><i class="fa fa-hashtag"></i> ID</th>
                                <th><i class="fa fa-user"></i> 用户名</th>
                                <th><i class="fa fa-lock"></i> 密码</th>
                                <th><i class="fa fa-envelope"></i> 邮箱</th>
                                <th><i class="fa fa-phone"></i> 电话</th>
                                <th><i class="fa fa-birthday-cake"></i> 年龄</th>
                                <th><i class="fa fa-cogs"></i> 操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in data_list %}
                            <tr data-user-id="{{ user.id }}">
                                <td>
                                    <span class="user-id">{{ user.id }}</span>
                                </td>
                                <td class="user-info">
                                    <strong>{{ user.username }}</strong>
                                </td>
                                <td class="user-info">
                                    <span style="font-family: monospace;">{{ user.password }}</span>
                                </td>
                                <td class="user-info">
                                    <i class="fa fa-envelope-o"></i> {{ user.email }}
                                </td>
                                <td class="user-info">
                                    <i class="fa fa-phone"></i> {{ user.phone }}
                                </td>
                                <td class="user-info">
                                    <span class="badge" style="background: #17a2b8;">{{ user.age }}岁</span>
                                </td>
                                <td>
                                    <a href="/user/delete/?id={{ user.id }}" 
                                       class="delete-btn" 
                                       onclick="return confirmDelete('{{ user.username }}')">
                                        <i class="fa fa-trash"></i> 删除
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" style="text-align: center; padding: 50px; color: #666;">
                                    <i class="fa fa-inbox" style="font-size: 48px; margin-bottom: 20px; display: block;"></i>
                                    暂无用户数据，点击上方按钮添加新用户
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <a href="/user/add/" class="floating-action" title="快速添加用户">
        <i class="fa fa-plus"></i>
    </a>
    
    <script src="../static/js/jquery-3.6.0.min.js"></script>
    <script src="../static/plugins/bootstrap-3.4.1/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            // 页面加载动画
            $('#loading').show();
            $('#tableContainer').hide();
            
            setTimeout(function() {
                $('#loading').fadeOut(500, function() {
                    $('#tableContainer').fadeIn(500);
                });
            }, 800);
            
            // 表格行点击效果
            $('tbody tr').click(function(e) {
                if (!$(e.target).closest('a').length) {
                    var $this = $(this);
                    $this.addClass('table-active');
                    setTimeout(function() {
                        $this.removeClass('table-active');
                    }, 200);
                }
            });
            
            // 删除按钮悬停效果
            $('.delete-btn').hover(
                function() {
                    $(this).find('i').addClass('fa-spin');
                },
                function() {
                    $(this).find('i').removeClass('fa-spin');
                }
            );
            
            // 添加按钮点击效果
            $('.add-btn').click(function(e) {
                var $this = $(this);
                $this.addClass('animated pulse');
                setTimeout(function() {
                    $this.removeClass('animated pulse');
                }, 600);
            });
            
            // 浮动按钮点击效果
            $('.floating-action').click(function(e) {
                var $this = $(this);
                $this.css('transform', 'scale(0.9)');
                setTimeout(function() {
                    $this.css('transform', '');
                }, 150);
            });
            
            // 表格行数据统计动画
            var userCount = parseInt('{{ data_list|length }}');
            if (userCount > 0) {
                var counter = 0;
                var interval = setInterval(function() {
                    counter++;
                    $('.stats-bar strong').text(counter);
                    if (counter >= userCount) {
                        clearInterval(interval);
                    }
                }, 100);
            }
        });
        
        // 删除确认对话框
        function confirmDelete(username) {
            return confirm('确定要删除用户 "' + username + '" 吗？\n此操作不可撤销！');
        }
        
        // 页面滚动效果
        $(window).scroll(function() {
            var scrollTop = $(window).scrollTop();
            $('.main-container').css('transform', 'translateY(' + (scrollTop * 0.1) + 'px)');
        });
    </script>
</body>
</html>
