import os
import sys
import django

# 添加项目路径到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置Django设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangostudy.settings')

try:
    # 初始化Django
    django.setup()
    print("Django setup successful!")
    
    # 测试导入模型
    from ljjapp.models import UserInfo
    print("Model import successful!")
    
    # 测试数据库连接
    from django.db import connection
    cursor = connection.cursor()
    cursor.execute("SELECT 1")
    print("Database connection successful!")
    
    # 测试创建用户
    try:
        user = UserInfo.objects.create(
            username="test_user_" + str(os.getpid()),
            password="test123",
            email=f"test{os.getpid()}@example.com",
            phone=f"1234567890{os.getpid() % 10}",
            age=25
        )
        print(f"User created successfully: {user.username}")
        
        # 查询用户
        users = UserInfo.objects.all()
        print(f"Total users in database: {users.count()}")
        
    except Exception as e:
        print(f"Error creating user: {e}")
        
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
