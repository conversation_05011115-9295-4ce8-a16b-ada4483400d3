from django.db import models

# Create your models here.
class Department(models.Model):

    '''部门表'''
    id=models.BigAutoField(verbose_name='ID',primary_key=True)
    title=models.CharField(verbose_name='标题',max_length=32)

    def __str__(self):
        return self.title

class UserInfo(models.Model):
    '''用户表'''
    id=models.BigAutoField(verbose_name='ID',primary_key=True)
    name=models.CharField(verbose_name='用户名',max_length=32)
    password=models.CharField(verbose_name='密码',max_length=64)
    age=models.IntegerField(verbose_name='年龄')
    account=models.DecimalField(verbose_name='账户余额',max_digits=10,decimal_places=2,default=0)
    create_time=models.DateField(verbose_name='入职时间')
    depart=models.ForeignKey(verbose_name='部门',to='Department',to_field='id',on_delete=models.CASCADE)
