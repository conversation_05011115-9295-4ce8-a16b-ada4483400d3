from django.urls import path
from . import views

urlpatterns = [
    #部门管理
    path('', views.home, name='newapp_home'),
    path('about/', views.about, name='newapp_about'),
    path('contact/', views.contact, name='newapp_contact'),
    path('products/', views.products, name='newapp_products'),
    path('depart/list/',views.depart_list,name='newapp_depart'),
    path('depart/add/',views.depart_add,name='newapp_depart_add'),
    path('delete/',views.delete_depart,name='newapp_delete'),
    path('edit/',views.edit_depart,name='newapp_edit'),
   
   #用户管理

   path('depart/user/list/',views.depart_userlist,name='newapp_depart_userlist'),
   path('depart/user/add/',views.depart_user_add,name='newapp_depart_user_add'),
   path('depart/user/edit/',views.depart_user_edit,name='newapp_depart_user_edit'),
   path('depart/user/delete/',views.depart_user_delete,name='newapp_depart_user_delete'),

   path('depart/user/model/add/',views.depart_user_model_add,name='newapp_depart_user_model_add')
]
