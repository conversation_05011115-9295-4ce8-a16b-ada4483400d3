<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <title>title</title>
</head>

<body>
    <span id="text">欢迎宝贝光临寒舍</span>
    <script type="text/javascript">
        function show() {
            // 获取元素
            var textElement = document.getElementById("text");
            // 修改元素内容
            var dataString = textElement.innerText;
            //动态起来
            var firstChar = dataString.charAt(0);
            var otherString = dataString.substring(1, dataString.length);
            textElement.innerText = otherString + firstChar;
        }
        setInterval(show, 1000);
    </script>
</body>

</html>