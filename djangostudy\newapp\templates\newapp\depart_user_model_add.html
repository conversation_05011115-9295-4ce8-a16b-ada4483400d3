{% extends 'newapp/content.html' %}

{% block title %}
新建ModelForm用户
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-10 col-md-offset-1">
            <div class="page-header">
                <h2>
                    <span class="glyphicon glyphicon-cog" aria-hidden="true"></span>
                    新建ModelForm用户
                    <small>使用Django ModelForm组件添加用户</small>
                </h2>
            </div>

            <div class="panel panel-primary">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <span class="glyphicon glyphicon-user-plus" aria-hidden="true"></span>
                        ModelForm用户信息录入
                        <span class="badge pull-right">智能表单</span>
                    </h3>
                </div>
                <div class="panel-body">
                    {% if form.errors %}
                        <div class="alert alert-danger alert-dismissible">
                            <button type="button" class="close" data-dismiss="alert">&times;</button>
                            <h4><span class="glyphicon glyphicon-exclamation-sign"></span> 表单验证错误</h4>
                            {% for field, errors in form.errors.items %}
                                <p><strong>{{ field }}:</strong> {{ errors|join:", " }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    {% if error %}
                        <div class="alert alert-warning alert-dismissible">
                            <button type="button" class="close" data-dismiss="alert">&times;</button>
                            <span class="glyphicon glyphicon-warning-sign"></span>
                            {{ error }}
                        </div>
                    {% endif %}
                    
                    <form method="POST" action="/new/depart/user/model/add/" class="form-horizontal">
                        {% csrf_token %}
                        {% for field in form %}
                            <div class="form-group {% if field.errors %}has-error{% endif %}">
                                <label class="col-sm-3 control-label">
                                    {{ field.label_tag }}
                                    {% if field.field.required %}
                                        <span class="text-danger">*</span>
                                    {% endif %}
                                </label>
                                <div class="col-sm-9">
                                    {{ field }}
                                    {% if field.errors %}
                                        <span class="help-block">
                                            <span class="glyphicon glyphicon-exclamation-sign"></span>
                                            {{ field.errors|join:", " }}
                                        </span>
                                    {% endif %}
                                    {% if field.help_text %}
                                        <span class="help-block text-muted">{{ field.help_text }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        {% endfor %}
                        
                        <div class="form-group">
                            <div class="col-sm-offset-3 col-sm-9">
                                <div class="btn-group btn-group-lg">
                                    <button type="submit" class="btn btn-primary">
                                        <span class="glyphicon glyphicon-floppy-disk"></span>
                                        保存用户
                                    </button>
                                    <a href="/new/depart/user/list/" class="btn btn-default">
                                        <span class="glyphicon glyphicon-arrow-left"></span>
                                        返回列表
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="panel-footer">
                    <div class="row">
                        <div class="col-sm-6">
                            <small class="text-muted">
                                <span class="glyphicon glyphicon-info-sign"></span>
                                使用Django ModelForm自动生成表单
                            </small>
                        </div>
                        <div class="col-sm-6 text-right">
                            <small class="text-muted">
                                <span class="glyphicon glyphicon-asterisk"></span>
                                标记为必填字段
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* 自定义样式 */
.page-header {
    border-bottom: 3px solid #337ab7;
    padding-bottom: 20px;
    margin-bottom: 30px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-radius: 5px;
}

.page-header h2 {
    margin-top: 0;
    color: #337ab7;
}

.panel-primary > .panel-heading {
    background: linear-gradient(135deg, #337ab7 0%, #286090 100%);
    border-color: #286090;
}

.panel-primary > .panel-heading .badge {
    background-color: #fff;
    color: #337ab7;
}

.form-horizontal .control-label {
    font-weight: bold;
    color: #555;
    padding-top: 7px;
}

.form-control {
    border-radius: 4px;
    border: 2px solid #ddd;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #337ab7;
    box-shadow: 0 0 8px rgba(51, 122, 183, 0.3);
}

.has-error .form-control {
    border-color: #d9534f;
}

.has-error .help-block {
    color: #d9534f;
    font-weight: bold;
}

.btn-group-lg .btn {
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #337ab7 0%, #286090 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #286090 0%, #204d74 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-default:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.panel-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #ddd;
}

.alert {
    border-radius: 6px;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.alert-danger {
    background: linear-gradient(135deg, #f2dede 0%, #ebcccc 100%);
    color: #a94442;
}

.alert-warning {
    background: linear-gradient(135deg, #fcf8e3 0%, #f7ecb5 100%);
    color: #8a6d3b;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-horizontal .control-label {
        text-align: left;
        padding-top: 0;
        margin-bottom: 5px;
    }
    
    .col-sm-offset-3 {
        margin-left: 0;
    }
    
    .btn-group-lg .btn {
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }
}

/* 动画效果 */
.panel {
    animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

{% endblock %}
