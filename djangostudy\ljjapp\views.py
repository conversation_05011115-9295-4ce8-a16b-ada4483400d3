from django.shortcuts import render,redirect,HttpResponse 
from .models import UserInfo

# Create your views here.
def index(request):
    return HttpResponse("欢迎使用django框架")
def user_list(request):
    #模拟数据列表
    user_list = [
        "张三","李四", "王五", "赵六"
    ]

    #将数据传递给前端
    return HttpResponse("<h1>用户列表</h1>" + "<br>".join(user_list))

def add_user(request):
    
    # 模拟数据
   
    name="zhaoming"
    age="18"
    salary=10000
    user_list = [
        "张三","李四", "王五", "赵六"
    ]
    user={
        "name": "梁佳杰",
        "age": "18",
        "salary": 10000
    }


    return render(request,"add_user.html",{"name":name,"age":age,"salary":salary,"user_list":user_list,"user":user})



def something(request):
    
    print(request.GET)
    print(request.method)
    m=request.GET.get('n2')
    print(type(m))

    # return HttpResponse("返回成功")
    return redirect("https://www.baidu.com")



def login(request):
    if request.method == "GET":
        return render(request, "login.html")
    else:
        print(request.POST)
        return HttpResponse("登录成功")
    
def new_user_orm(request):

    UserInfo.objects.create(username="new_user", password="password123", email="<EMAIL>", phone="12378901", age=5 )
    return HttpResponse("新用户添加成功")


def user_list_orm(request):
    #获取数据库中所有数据
    data_list=UserInfo.objects.all()
    print(data_list)
    return render(request,"user_list_orm.html",{"data_list":data_list})

def add_user_orm(request):
    if request.method == "GET":
        return render(request,"add_user_orm.html")
    username = request.POST.get("username")
    password = request.POST.get("password")
    email = request.POST.get("email")   
    phone = request.POST.get("phone")
    age = request.POST.get("age")
    print(username, password, email, phone, age)
    #创建新用户
    UserInfo.objects.create(username=username, password=password, email=email, phone=phone, age=age)
    # return HttpResponse("用户添加成功")
    return redirect("http://127.0.0.1:8000/user/list/")

def delete_orm(request):
    mid=request.GET.get("id")
    UserInfo.objects.filter(id=mid).delete()
    return redirect("/user/list/")
    


