{"version": 3, "sources": ["../../scss/mixins/_banner.scss", "../../scss/_root.scss", "../../scss/vendor/_rfs.scss", "bootstrap-utilities.css", "../../scss/mixins/_color-mode.scss", "../../scss/mixins/_clearfix.scss", "../../scss/helpers/_color-bg.scss", "../../scss/helpers/_colored-links.scss", "../../scss/helpers/_ratio.scss", "../../scss/helpers/_position.scss", "../../scss/_variables.scss", "../../scss/mixins/_breakpoints.scss", "../../scss/helpers/_stacks.scss", "../../scss/helpers/_visually-hidden.scss", "../../scss/mixins/_visually-hidden.scss", "../../scss/helpers/_stretched-link.scss", "../../scss/helpers/_text-truncation.scss", "../../scss/mixins/_text-truncate.scss", "../../scss/helpers/_vr.scss", "../../scss/mixins/_utilities.scss", "../../scss/utilities/_api.scss"], "names": [], "mappings": "AACE;;;;EAAA;ACDF;;EASI,kBAAA;EAAA,oBAAA;EAAA,oBAAA;EAAA,kBAAA;EAAA,iBAAA;EAAA,oBAAA;EAAA,oBAAA;EAAA,mBAAA;EAAA,kBAAA;EAAA,kBAAA;EAAA,gBAAA;EAAA,gBAAA;EAAA,kBAAA;EAAA,uBAAA;EAIA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAAA,sBAAA;EAIA,qBAAA;EAAA,uBAAA;EAAA,qBAAA;EAAA,kBAAA;EAAA,qBAAA;EAAA,oBAAA;EAAA,mBAAA;EAAA,kBAAA;EAIA,8BAAA;EAAA,iCAAA;EAAA,6BAAA;EAAA,2BAAA;EAAA,6BAAA;EAAA,4BAAA;EAAA,6BAAA;EAAA,yBAAA;EAIA,0BAAA;EAAA,4BAAA;EAAA,0BAAA;EAAA,uBAAA;EAAA,0BAAA;EAAA,yBAAA;EAAA,wBAAA;EAAA,uBAAA;EAIA,+BAAA;EAAA,iCAAA;EAAA,+BAAA;EAAA,4BAAA;EAAA,+BAAA;EAAA,8BAAA;EAAA,6BAAA;EAAA,4BAAA;EAIA,mCAAA;EAAA,qCAAA;EAAA,mCAAA;EAAA,gCAAA;EAAA,mCAAA;EAAA,kCAAA;EAAA,iCAAA;EAAA,gCAAA;EAGF,6BAAA;EACA,uBAAA;EACA,+BAAA;EACA,+BAAA;EAMA,qNAAA;EACA,yGAAA;EACA,yFAAA;EAOA,gDAAA;EC+OI,yBALI;EDxOR,0BAAA;EACA,0BAAA;EACA,wBAAA;EAEA,yBAAA;EACA,gCAAA;EAEA,4CAAA;EACA,oCAAA;EACA,0BAAA;EACA,oCAAA;EAEA,0CAAA;EACA,mCAAA;EACA,yBAAA;EACA,mCAAA;EAKA,kBAAA;EACA,+BAAA;EAOA,wBAAA;EACA,iCAAA;EACA,+BAAA;EAEA,8BAAA;EACA,sCAAA;EAMA,wBAAA;EACA,0BAAA;EAGA,sBAAA;EACA,wBAAA;EACA,0BAAA;EACA,mDAAA;EAEA,4BAAA;EACA,8BAAA;EACA,6BAAA;EACA,2BAAA;EACA,4BAAA;EACA,8BAAA;EAGA,mEAAA;EACA,4EAAA;EACA,qEAAA;EACA,4EAAA;EAEA,yBAAA;EAGA,uCAAA;EACA,qDAAA;EAGA,0BAAA;EAGE,qBAAA;EAAA,yBAAA;EAAA,yBAAA;EAAA,yBAAA;EAAA,0BAAA;EAAA,2BAAA;AEFJ;;AC9GI;EHuHA,wBAAA;EACA,kCAAA;EACA,qBAAA;EACA,4BAAA;EAEA,4BAAA;EACA,sCAAA;EAEA,+CAAA;EACA,uCAAA;EACA,0BAAA;EACA,iCAAA;EAEA,6CAAA;EACA,sCAAA;EACA,yBAAA;EACA,gCAAA;EAEA,yBAAA;EAEA,0BAAA;EACA,4BAAA;EACA,0BAAA;EACA,uBAAA;EACA,0BAAA;EACA,yBAAA;EACA,wBAAA;EACA,uBAAA;EAEA,+BAAA;EACA,iCAAA;EACA,+BAAA;EACA,4BAAA;EACA,+BAAA;EACA,8BAAA;EACA,6BAAA;EACA,4BAAA;EAEA,mCAAA;EACA,qCAAA;EACA,mCAAA;EACA,gCAAA;EACA,mCAAA;EACA,kCAAA;EACA,iCAAA;EACA,gCAAA;EAEA,wBAAA;EAEA,wBAAA;EACA,8BAAA;EACA,kCAAA;EACA,wCAAA;EAEA,wBAAA;EAEA,0BAAA;EACA,wDAAA;AEhBJ;;AE7KE;EACE,cAAA;EACA,WAAA;EACA,WAAA;AFgLJ;;AGhLE;EACE,sBAAA;EACA,wEAAA;AHmLJ;;AGrLE;EACE,sBAAA;EACA,yEAAA;AHwLJ;;AG1LE;EACE,sBAAA;EACA,uEAAA;AH6LJ;;AG/LE;EACE,sBAAA;EACA,wEAAA;AHkMJ;;AGpME;EACE,sBAAA;EACA,uEAAA;AHuMJ;;AGzME;EACE,sBAAA;EACA,uEAAA;AH4MJ;;AG9ME;EACE,sBAAA;EACA,yEAAA;AHiNJ;;AGnNE;EACE,sBAAA;EACA,sEAAA;AHsNJ;;AI5NE;EACE,yBAAA;AJ+NJ;AI5NM;EAEE,yBAAA;AJ6NR;;AInOE;EACE,yBAAA;AJsOJ;AInOM;EAEE,yBAAA;AJoOR;;AI1OE;EACE,yBAAA;AJ6OJ;AI1OM;EAEE,yBAAA;AJ2OR;;AIjPE;EACE,yBAAA;AJoPJ;AIjPM;EAEE,yBAAA;AJkPR;;AIxPE;EACE,yBAAA;AJ2PJ;AIxPM;EAEE,yBAAA;AJyPR;;AI/PE;EACE,yBAAA;AJkQJ;AI/PM;EAEE,yBAAA;AJgQR;;AItQE;EACE,yBAAA;AJyQJ;AItQM;EAEE,yBAAA;AJuQR;;AI7QE;EACE,yBAAA;AJgRJ;AI7QM;EAEE,yBAAA;AJ8QR;;AKnRA;EACE,kBAAA;EACA,WAAA;ALsRF;AKpRE;EACE,cAAA;EACA,mCAAA;EACA,WAAA;ALsRJ;AKnRE;EACE,kBAAA;EACA,MAAA;EACA,OAAA;EACA,WAAA;EACA,YAAA;ALqRJ;;AKhRE;EACE,uBAAA;ALmRJ;;AKpRE;EACE,sBAAA;ALuRJ;;AKxRE;EACE,yBAAA;AL2RJ;;AK5RE;EACE,iCAAA;AL+RJ;;AMpTA;EACE,eAAA;EACA,MAAA;EACA,QAAA;EACA,OAAA;EACA,aC+jCkC;APxwBpC;;AMpTA;EACE,eAAA;EACA,QAAA;EACA,SAAA;EACA,OAAA;EACA,aCujCkC;APhwBpC;;AM/SI;EACE,wBAAA;EAAA,gBAAA;EACA,MAAA;EACA,aC2iC8B;APzvBpC;;AM/SI;EACE,wBAAA;EAAA,gBAAA;EACA,SAAA;EACA,aCqiC8B;APnvBpC;;AQnRI;EFxCA;IACE,wBAAA;IAAA,gBAAA;IACA,MAAA;IACA,aC2iC8B;EP5uBlC;EM5TE;IACE,wBAAA;IAAA,gBAAA;IACA,SAAA;IACA,aCqiC8B;EPvuBlC;AACF;AQhSI;EFxCA;IACE,wBAAA;IAAA,gBAAA;IACA,MAAA;IACA,aC2iC8B;EPhuBlC;EMxUE;IACE,wBAAA;IAAA,gBAAA;IACA,SAAA;IACA,aCqiC8B;EP3tBlC;AACF;AQ5SI;EFxCA;IACE,wBAAA;IAAA,gBAAA;IACA,MAAA;IACA,aC2iC8B;EPptBlC;EMpVE;IACE,wBAAA;IAAA,gBAAA;IACA,SAAA;IACA,aCqiC8B;EP/sBlC;AACF;AQxTI;EFxCA;IACE,wBAAA;IAAA,gBAAA;IACA,MAAA;IACA,aC2iC8B;EPxsBlC;EMhWE;IACE,wBAAA;IAAA,gBAAA;IACA,SAAA;IACA,aCqiC8B;EPnsBlC;AACF;AQpUI;EFxCA;IACE,wBAAA;IAAA,gBAAA;IACA,MAAA;IACA,aC2iC8B;EP5rBlC;EM5WE;IACE,wBAAA;IAAA,gBAAA;IACA,SAAA;IACA,aCqiC8B;EPvrBlC;AACF;AS9YA;EACE,aAAA;EACA,mBAAA;EACA,mBAAA;EACA,mBAAA;ATgZF;;AS7YA;EACE,aAAA;EACA,cAAA;EACA,sBAAA;EACA,mBAAA;ATgZF;;AUxZA;;ECIE,6BAAA;EACA,qBAAA;EACA,sBAAA;EACA,qBAAA;EACA,uBAAA;EACA,2BAAA;EACA,iCAAA;EACA,8BAAA;EACA,oBAAA;AXyZF;;AYpaE;EACE,kBAAA;EACA,MAAA;EACA,QAAA;EACA,SAAA;EACA,OAAA;EACA,UL4bsC;EK3btC,WAAA;AZuaJ;;Aa/aA;ECAE,gBAAA;EACA,uBAAA;EACA,mBAAA;AdmbF;;AezbA;EACE,qBAAA;EACA,mBAAA;EACA,UAAA;EACA,eAAA;EACA,8BAAA;EACA,aRiqB4B;APrO9B;;AgBhYQ;EAOI,mCAAA;AhB6XZ;;AgBpYQ;EAOI,8BAAA;AhBiYZ;;AgBxYQ;EAOI,iCAAA;AhBqYZ;;AgB5YQ;EAOI,iCAAA;AhByYZ;;AgBhZQ;EAOI,sCAAA;AhB6YZ;;AgBpZQ;EAOI,mCAAA;AhBiZZ;;AgBxZQ;EAOI,sBAAA;AhBqZZ;;AgB5ZQ;EAOI,uBAAA;AhByZZ;;AgBhaQ;EAOI,sBAAA;AhB6ZZ;;AgBpaQ;EAOI,iCAAA;EAAA,8BAAA;AhBiaZ;;AgBxaQ;EAOI,+BAAA;EAAA,4BAAA;AhBqaZ;;AgB5aQ;EAOI,8BAAA;EAAA,2BAAA;AhByaZ;;AgBhbQ;EAOI,oCAAA;EAAA,iCAAA;AhB6aZ;;AgBpbQ;EAOI,8BAAA;EAAA,2BAAA;AhBibZ;;AgBxbQ;EAOI,qBAAA;AhBqbZ;;AgB5bQ;EAOI,wBAAA;AhBybZ;;AgBhcQ;EAOI,uBAAA;AhB6bZ;;AgBpcQ;EAOI,wBAAA;AhBicZ;;AgBxcQ;EAOI,qBAAA;AhBqcZ;;AgB5cQ;EAOI,yBAAA;AhBycZ;;AgBhdQ;EAOI,2BAAA;AhB6cZ;;AgBpdQ;EAOI,4BAAA;AhBidZ;;AgBxdQ;EAOI,2BAAA;AhBqdZ;;AgB5dQ;EAOI,2BAAA;AhBydZ;;AgBheQ;EAOI,6BAAA;AhB6dZ;;AgBpeQ;EAOI,8BAAA;AhBieZ;;AgBxeQ;EAOI,6BAAA;AhBqeZ;;AgB5eQ;EAOI,2BAAA;AhByeZ;;AgBhfQ;EAOI,6BAAA;AhB6eZ;;AgBpfQ;EAOI,8BAAA;AhBifZ;;AgBxfQ;EAOI,6BAAA;AhBqfZ;;AgB5fQ;EAOI,0BAAA;AhByfZ;;AgBhgBQ;EAOI,gCAAA;AhB6fZ;;AgBpgBQ;EAOI,yBAAA;AhBigBZ;;AgBxgBQ;EAOI,wBAAA;AhBqgBZ;;AgB5gBQ;EAOI,yBAAA;AhBygBZ;;AgBhhBQ;EAOI,6BAAA;AhB6gBZ;;AgBphBQ;EAOI,8BAAA;AhBihBZ;;AgBxhBQ;EAOI,wBAAA;AhBqhBZ;;AgB5hBQ;EAOI,+BAAA;AhByhBZ;;AgBhiBQ;EAOI,wBAAA;AhB6hBZ;;AgBpiBQ;EAOI,yEAAA;AhBiiBZ;;AgBxiBQ;EAOI,+EAAA;AhBqiBZ;;AgB5iBQ;EAOI,wEAAA;AhByiBZ;;AgBhjBQ;EAOI,2BAAA;AhB6iBZ;;AgBpjBQ;EAOI,2BAAA;AhBijBZ;;AgBxjBQ;EAOI,6BAAA;AhBqjBZ;;AgB5jBQ;EAOI,6BAAA;AhByjBZ;;AgBhkBQ;EAOI,0BAAA;AhB6jBZ;;AgBpkBQ;EAOI,mCAAA;EAAA,2BAAA;AhBikBZ;;AgBxkBQ;EAOI,iBAAA;AhBqkBZ;;AgB5kBQ;EAOI,mBAAA;AhBykBZ;;AgBhlBQ;EAOI,oBAAA;AhB6kBZ;;AgBplBQ;EAOI,oBAAA;AhBilBZ;;AgBxlBQ;EAOI,sBAAA;AhBqlBZ;;AgB5lBQ;EAOI,uBAAA;AhBylBZ;;AgBhmBQ;EAOI,kBAAA;AhB6lBZ;;AgBpmBQ;EAOI,oBAAA;AhBimBZ;;AgBxmBQ;EAOI,qBAAA;AhBqmBZ;;AgB5mBQ;EAOI,mBAAA;AhBymBZ;;AgBhnBQ;EAOI,qBAAA;AhB6mBZ;;AgBpnBQ;EAOI,sBAAA;AhBinBZ;;AgBxnBQ;EAOI,2CAAA;AhBqnBZ;;AgB5nBQ;EAOI,sCAAA;AhBynBZ;;AgBhoBQ;EAOI,sCAAA;AhB6nBZ;;AgBpoBQ;EAOI,uFAAA;AhBioBZ;;AgBxoBQ;EAOI,oBAAA;AhBqoBZ;;AgB5oBQ;EAOI,2FAAA;AhByoBZ;;AgBhpBQ;EAOI,wBAAA;AhB6oBZ;;AgBppBQ;EAOI,6FAAA;AhBipBZ;;AgBxpBQ;EAOI,0BAAA;AhBqpBZ;;AgB5pBQ;EAOI,8FAAA;AhBypBZ;;AgBhqBQ;EAOI,2BAAA;AhB6pBZ;;AgBpqBQ;EAOI,4FAAA;AhBiqBZ;;AgBxqBQ;EAOI,yBAAA;AhBqqBZ;;AgB5qBQ;EAIQ,sBAAA;EAGJ,8EAAA;AhB0qBZ;;AgBjrBQ;EAIQ,sBAAA;EAGJ,gFAAA;AhB+qBZ;;AgBtrBQ;EAIQ,sBAAA;EAGJ,8EAAA;AhBorBZ;;AgB3rBQ;EAIQ,sBAAA;EAGJ,2EAAA;AhByrBZ;;AgBhsBQ;EAIQ,sBAAA;EAGJ,8EAAA;AhB8rBZ;;AgBrsBQ;EAIQ,sBAAA;EAGJ,6EAAA;AhBmsBZ;;AgB1sBQ;EAIQ,sBAAA;EAGJ,4EAAA;AhBwsBZ;;AgB/sBQ;EAIQ,sBAAA;EAGJ,2EAAA;AhB6sBZ;;AgBptBQ;EAIQ,sBAAA;EAGJ,4EAAA;AhBktBZ;;AgBztBQ;EAOI,wDAAA;AhBstBZ;;AgB7tBQ;EAOI,0DAAA;AhB0tBZ;;AgBjuBQ;EAOI,wDAAA;AhB8tBZ;;AgBruBQ;EAOI,qDAAA;AhBkuBZ;;AgBzuBQ;EAOI,wDAAA;AhBsuBZ;;AgB7uBQ;EAOI,uDAAA;AhB0uBZ;;AgBjvBQ;EAOI,sDAAA;AhB8uBZ;;AgBrvBQ;EAOI,qDAAA;AhBkvBZ;;AgBnwBQ;EACE,sBAAA;AhBswBV;;AgBvwBQ;EACE,sBAAA;AhB0wBV;;AgB3wBQ;EACE,sBAAA;AhB8wBV;;AgB/wBQ;EACE,sBAAA;AhBkxBV;;AgBnxBQ;EACE,sBAAA;AhBsxBV;;AgBvxBQ;EACE,wBAAA;AhB0xBV;;AgB3xBQ;EACE,yBAAA;AhB8xBV;;AgB/xBQ;EACE,wBAAA;AhBkyBV;;AgBnyBQ;EACE,yBAAA;AhBsyBV;;AgBvyBQ;EACE,sBAAA;AhB0yBV;;AgBjyBQ;EAOI,qBAAA;AhB8xBZ;;AgBryBQ;EAOI,qBAAA;AhBkyBZ;;AgBzyBQ;EAOI,qBAAA;AhBsyBZ;;AgB7yBQ;EAOI,sBAAA;AhB0yBZ;;AgBjzBQ;EAOI,sBAAA;AhB8yBZ;;AgBrzBQ;EAOI,0BAAA;AhBkzBZ;;AgBzzBQ;EAOI,uBAAA;AhBszBZ;;AgB7zBQ;EAOI,2BAAA;AhB0zBZ;;AgBj0BQ;EAOI,sBAAA;AhB8zBZ;;AgBr0BQ;EAOI,sBAAA;AhBk0BZ;;AgBz0BQ;EAOI,sBAAA;AhBs0BZ;;AgB70BQ;EAOI,uBAAA;AhB00BZ;;AgBj1BQ;EAOI,uBAAA;AhB80BZ;;AgBr1BQ;EAOI,2BAAA;AhBk1BZ;;AgBz1BQ;EAOI,wBAAA;AhBs1BZ;;AgB71BQ;EAOI,4BAAA;AhB01BZ;;AgBj2BQ;EAOI,yBAAA;AhB81BZ;;AgBr2BQ;EAOI,8BAAA;AhBk2BZ;;AgBz2BQ;EAOI,iCAAA;AhBs2BZ;;AgB72BQ;EAOI,sCAAA;AhB02BZ;;AgBj3BQ;EAOI,yCAAA;AhB82BZ;;AgBr3BQ;EAOI,uBAAA;AhBk3BZ;;AgBz3BQ;EAOI,uBAAA;AhBs3BZ;;AgB73BQ;EAOI,yBAAA;AhB03BZ;;AgBj4BQ;EAOI,yBAAA;AhB83BZ;;AgBr4BQ;EAOI,0BAAA;AhBk4BZ;;AgBz4BQ;EAOI,4BAAA;AhBs4BZ;;AgB74BQ;EAOI,kCAAA;AhB04BZ;;AgBj5BQ;EAOI,sCAAA;AhB84BZ;;AgBr5BQ;EAOI,oCAAA;AhBk5BZ;;AgBz5BQ;EAOI,kCAAA;AhBs5BZ;;AgB75BQ;EAOI,yCAAA;AhB05BZ;;AgBj6BQ;EAOI,wCAAA;AhB85BZ;;AgBr6BQ;EAOI,wCAAA;AhBk6BZ;;AgBz6BQ;EAOI,kCAAA;AhBs6BZ;;AgB76BQ;EAOI,gCAAA;AhB06BZ;;AgBj7BQ;EAOI,8BAAA;AhB86BZ;;AgBr7BQ;EAOI,gCAAA;AhBk7BZ;;AgBz7BQ;EAOI,+BAAA;AhBs7BZ;;AgB77BQ;EAOI,oCAAA;AhB07BZ;;AgBj8BQ;EAOI,kCAAA;AhB87BZ;;AgBr8BQ;EAOI,gCAAA;AhBk8BZ;;AgBz8BQ;EAOI,uCAAA;AhBs8BZ;;AgB78BQ;EAOI,sCAAA;AhB08BZ;;AgBj9BQ;EAOI,iCAAA;AhB88BZ;;AgBr9BQ;EAOI,2BAAA;AhBk9BZ;;AgBz9BQ;EAOI,iCAAA;AhBs9BZ;;AgB79BQ;EAOI,+BAAA;AhB09BZ;;AgBj+BQ;EAOI,6BAAA;AhB89BZ;;AgBr+BQ;EAOI,+BAAA;AhBk+BZ;;AgBz+BQ;EAOI,8BAAA;AhBs+BZ;;AgB7+BQ;EAOI,oBAAA;AhB0+BZ;;AgBj/BQ;EAOI,mBAAA;AhB8+BZ;;AgBr/BQ;EAOI,mBAAA;AhBk/BZ;;AgBz/BQ;EAOI,mBAAA;AhBs/BZ;;AgB7/BQ;EAOI,mBAAA;AhB0/BZ;;AgBjgCQ;EAOI,mBAAA;AhB8/BZ;;AgBrgCQ;EAOI,mBAAA;AhBkgCZ;;AgBzgCQ;EAOI,mBAAA;AhBsgCZ;;AgB7gCQ;EAOI,oBAAA;AhB0gCZ;;AgBjhCQ;EAOI,0BAAA;AhB8gCZ;;AgBrhCQ;EAOI,yBAAA;AhBkhCZ;;AgBzhCQ;EAOI,uBAAA;AhBshCZ;;AgB7hCQ;EAOI,yBAAA;AhB0hCZ;;AgBjiCQ;EAOI,uBAAA;AhB8hCZ;;AgBriCQ;EAOI,uBAAA;AhBkiCZ;;AgBziCQ;EAOI,0BAAA;EAAA,yBAAA;AhBuiCZ;;AgB9iCQ;EAOI,gCAAA;EAAA,+BAAA;AhB4iCZ;;AgBnjCQ;EAOI,+BAAA;EAAA,8BAAA;AhBijCZ;;AgBxjCQ;EAOI,6BAAA;EAAA,4BAAA;AhBsjCZ;;AgB7jCQ;EAOI,+BAAA;EAAA,8BAAA;AhB2jCZ;;AgBlkCQ;EAOI,6BAAA;EAAA,4BAAA;AhBgkCZ;;AgBvkCQ;EAOI,6BAAA;EAAA,4BAAA;AhBqkCZ;;AgB5kCQ;EAOI,wBAAA;EAAA,2BAAA;AhB0kCZ;;AgBjlCQ;EAOI,8BAAA;EAAA,iCAAA;AhB+kCZ;;AgBtlCQ;EAOI,6BAAA;EAAA,gCAAA;AhBolCZ;;AgB3lCQ;EAOI,2BAAA;EAAA,8BAAA;AhBylCZ;;AgBhmCQ;EAOI,6BAAA;EAAA,gCAAA;AhB8lCZ;;AgBrmCQ;EAOI,2BAAA;EAAA,8BAAA;AhBmmCZ;;AgB1mCQ;EAOI,2BAAA;EAAA,8BAAA;AhBwmCZ;;AgB/mCQ;EAOI,wBAAA;AhB4mCZ;;AgBnnCQ;EAOI,8BAAA;AhBgnCZ;;AgBvnCQ;EAOI,6BAAA;AhBonCZ;;AgB3nCQ;EAOI,2BAAA;AhBwnCZ;;AgB/nCQ;EAOI,6BAAA;AhB4nCZ;;AgBnoCQ;EAOI,2BAAA;AhBgoCZ;;AgBvoCQ;EAOI,2BAAA;AhBooCZ;;AgB3oCQ;EAOI,0BAAA;AhBwoCZ;;AgB/oCQ;EAOI,gCAAA;AhB4oCZ;;AgBnpCQ;EAOI,+BAAA;AhBgpCZ;;AgBvpCQ;EAOI,6BAAA;AhBopCZ;;AgB3pCQ;EAOI,+BAAA;AhBwpCZ;;AgB/pCQ;EAOI,6BAAA;AhB4pCZ;;AgBnqCQ;EAOI,6BAAA;AhBgqCZ;;AgBvqCQ;EAOI,2BAAA;AhBoqCZ;;AgB3qCQ;EAOI,iCAAA;AhBwqCZ;;AgB/qCQ;EAOI,gCAAA;AhB4qCZ;;AgBnrCQ;EAOI,8BAAA;AhBgrCZ;;AgBvrCQ;EAOI,gCAAA;AhBorCZ;;AgB3rCQ;EAOI,8BAAA;AhBwrCZ;;AgB/rCQ;EAOI,8BAAA;AhB4rCZ;;AgBnsCQ;EAOI,yBAAA;AhBgsCZ;;AgBvsCQ;EAOI,+BAAA;AhBosCZ;;AgB3sCQ;EAOI,8BAAA;AhBwsCZ;;AgB/sCQ;EAOI,4BAAA;AhB4sCZ;;AgBntCQ;EAOI,8BAAA;AhBgtCZ;;AgBvtCQ;EAOI,4BAAA;AhBotCZ;;AgB3tCQ;EAOI,4BAAA;AhBwtCZ;;AgB/tCQ;EAOI,qBAAA;AhB4tCZ;;AgBnuCQ;EAOI,2BAAA;AhBguCZ;;AgBvuCQ;EAOI,0BAAA;AhBouCZ;;AgB3uCQ;EAOI,wBAAA;AhBwuCZ;;AgB/uCQ;EAOI,0BAAA;AhB4uCZ;;AgBnvCQ;EAOI,wBAAA;AhBgvCZ;;AgBvvCQ;EAOI,2BAAA;EAAA,0BAAA;AhBqvCZ;;AgB5vCQ;EAOI,iCAAA;EAAA,gCAAA;AhB0vCZ;;AgBjwCQ;EAOI,gCAAA;EAAA,+BAAA;AhB+vCZ;;AgBtwCQ;EAOI,8BAAA;EAAA,6BAAA;AhBowCZ;;AgB3wCQ;EAOI,gCAAA;EAAA,+BAAA;AhBywCZ;;AgBhxCQ;EAOI,8BAAA;EAAA,6BAAA;AhB8wCZ;;AgBrxCQ;EAOI,yBAAA;EAAA,4BAAA;AhBmxCZ;;AgB1xCQ;EAOI,+BAAA;EAAA,kCAAA;AhBwxCZ;;AgB/xCQ;EAOI,8BAAA;EAAA,iCAAA;AhB6xCZ;;AgBpyCQ;EAOI,4BAAA;EAAA,+BAAA;AhBkyCZ;;AgBzyCQ;EAOI,8BAAA;EAAA,iCAAA;AhBuyCZ;;AgB9yCQ;EAOI,4BAAA;EAAA,+BAAA;AhB4yCZ;;AgBnzCQ;EAOI,yBAAA;AhBgzCZ;;AgBvzCQ;EAOI,+BAAA;AhBozCZ;;AgB3zCQ;EAOI,8BAAA;AhBwzCZ;;AgB/zCQ;EAOI,4BAAA;AhB4zCZ;;AgBn0CQ;EAOI,8BAAA;AhBg0CZ;;AgBv0CQ;EAOI,4BAAA;AhBo0CZ;;AgB30CQ;EAOI,2BAAA;AhBw0CZ;;AgB/0CQ;EAOI,iCAAA;AhB40CZ;;AgBn1CQ;EAOI,gCAAA;AhBg1CZ;;AgBv1CQ;EAOI,8BAAA;AhBo1CZ;;AgB31CQ;EAOI,gCAAA;AhBw1CZ;;AgB/1CQ;EAOI,8BAAA;AhB41CZ;;AgBn2CQ;EAOI,4BAAA;AhBg2CZ;;AgBv2CQ;EAOI,kCAAA;AhBo2CZ;;AgB32CQ;EAOI,iCAAA;AhBw2CZ;;AgB/2CQ;EAOI,+BAAA;AhB42CZ;;AgBn3CQ;EAOI,iCAAA;AhBg3CZ;;AgBv3CQ;EAOI,+BAAA;AhBo3CZ;;AgB33CQ;EAOI,0BAAA;AhBw3CZ;;AgB/3CQ;EAOI,gCAAA;AhB43CZ;;AgBn4CQ;EAOI,+BAAA;AhBg4CZ;;AgBv4CQ;EAOI,6BAAA;AhBo4CZ;;AgB34CQ;EAOI,+BAAA;AhBw4CZ;;AgB/4CQ;EAOI,6BAAA;AhB44CZ;;AgBn5CQ;EAOI,iBAAA;AhBg5CZ;;AgBv5CQ;EAOI,uBAAA;AhBo5CZ;;AgB35CQ;EAOI,sBAAA;AhBw5CZ;;AgB/5CQ;EAOI,oBAAA;AhB45CZ;;AgBn6CQ;EAOI,sBAAA;AhBg6CZ;;AgBv6CQ;EAOI,oBAAA;AhBo6CZ;;AgB36CQ;EAOI,qBAAA;AhBw6CZ;;AgB/6CQ;EAOI,2BAAA;AhB46CZ;;AgBn7CQ;EAOI,0BAAA;AhBg7CZ;;AgBv7CQ;EAOI,wBAAA;AhBo7CZ;;AgB37CQ;EAOI,0BAAA;AhBw7CZ;;AgB/7CQ;EAOI,wBAAA;AhB47CZ;;AgBn8CQ;EAOI,6BAAA;EAAA,wBAAA;AhBg8CZ;;AgBv8CQ;EAOI,mCAAA;EAAA,8BAAA;AhBo8CZ;;AgB38CQ;EAOI,kCAAA;EAAA,6BAAA;AhBw8CZ;;AgB/8CQ;EAOI,gCAAA;EAAA,2BAAA;AhB48CZ;;AgBn9CQ;EAOI,kCAAA;EAAA,6BAAA;AhBg9CZ;;AgBv9CQ;EAOI,gCAAA;EAAA,2BAAA;AhBo9CZ;;AgB39CQ;EAOI,gDAAA;AhBw9CZ;;AgB/9CQ;EAOI,4CAAA;AhB49CZ;;AgBn+CQ;EAOI,4CAAA;AhBg+CZ;;AgBv+CQ;EAOI,0CAAA;AhBo+CZ;;AgB3+CQ;EAOI,4CAAA;AhBw+CZ;;AgB/+CQ;EAOI,6BAAA;AhB4+CZ;;AgBn/CQ;EAOI,0BAAA;AhBg/CZ;;AgBv/CQ;EAOI,6BAAA;AhBo/CZ;;AgB3/CQ;EAOI,6BAAA;AhBw/CZ;;AgB//CQ;EAOI,+BAAA;AhB4/CZ;;AgBngDQ;EAOI,2BAAA;AhBggDZ;;AgBvgDQ;EAOI,2BAAA;AhBogDZ;;AgB3gDQ;EAOI,2BAAA;AhBwgDZ;;AgB/gDQ;EAOI,2BAAA;AhB4gDZ;;AgBnhDQ;EAOI,2BAAA;AhBghDZ;;AgBvhDQ;EAOI,8BAAA;AhBohDZ;;AgB3hDQ;EAOI,yBAAA;AhBwhDZ;;AgB/hDQ;EAOI,4BAAA;AhB4hDZ;;AgBniDQ;EAOI,2BAAA;AhBgiDZ;;AgBviDQ;EAOI,yBAAA;AhBoiDZ;;AgB3iDQ;EAOI,2BAAA;AhBwiDZ;;AgB/iDQ;EAOI,4BAAA;AhB4iDZ;;AgBnjDQ;EAOI,6BAAA;AhBgjDZ;;AgBvjDQ;EAOI,gCAAA;AhBojDZ;;AgB3jDQ;EAOI,qCAAA;AhBwjDZ;;AgB/jDQ;EAOI,wCAAA;AhB4jDZ;;AgBnkDQ;EAOI,oCAAA;AhBgkDZ;;AgBvkDQ;EAOI,oCAAA;AhBokDZ;;AgB3kDQ;EAOI,qCAAA;AhBwkDZ;;AgB/kDQ;EAOI,8BAAA;AhB4kDZ;;AgBnlDQ;EAOI,8BAAA;AhBglDZ;;AgBrmDQ,qBAAA;AAcA;EAOI,gCAAA;EAAA,iCAAA;AhBslDZ;;AgBnkDQ,mBAAA;AA1BA;EAIQ,oBAAA;EAGJ,qEAAA;AhB4lDZ;;AgBnmDQ;EAIQ,oBAAA;EAGJ,uEAAA;AhBimDZ;;AgBxmDQ;EAIQ,oBAAA;EAGJ,qEAAA;AhBsmDZ;;AgB7mDQ;EAIQ,oBAAA;EAGJ,kEAAA;AhB2mDZ;;AgBlnDQ;EAIQ,oBAAA;EAGJ,qEAAA;AhBgnDZ;;AgBvnDQ;EAIQ,oBAAA;EAGJ,oEAAA;AhBqnDZ;;AgB5nDQ;EAIQ,oBAAA;EAGJ,mEAAA;AhB0nDZ;;AgBjoDQ;EAIQ,oBAAA;EAGJ,kEAAA;AhB+nDZ;;AgBtoDQ;EAIQ,oBAAA;EAGJ,mEAAA;AhBooDZ;;AgB3oDQ;EAIQ,oBAAA;EAGJ,mEAAA;AhByoDZ;;AgBhpDQ;EAIQ,oBAAA;EAGJ,wEAAA;AhB8oDZ;;AgBrpDQ;EAIQ,oBAAA;EAGJ,2CAAA;AhBmpDZ;;AgB1pDQ;EAIQ,oBAAA;EAGJ,oCAAA;AhBwpDZ;;AgB/pDQ;EAIQ,oBAAA;EAGJ,0CAAA;AhB6pDZ;;AgBpqDQ;EAIQ,oBAAA;EAGJ,2CAAA;AhBkqDZ;;AgBzqDQ;EAIQ,oBAAA;EAGJ,0CAAA;AhBuqDZ;;AgB9qDQ;EAIQ,oBAAA;EAGJ,0CAAA;AhB4qDZ;;AgBnrDQ;EAIQ,oBAAA;EAGJ,yBAAA;AhBirDZ;;AgBlsDQ;EACE,uBAAA;AhBqsDV;;AgBtsDQ;EACE,sBAAA;AhBysDV;;AgB1sDQ;EACE,uBAAA;AhB6sDV;;AgB9sDQ;EACE,oBAAA;AhBitDV;;AgBxsDQ;EAOI,wCAAA;AhBqsDZ;;AgB5sDQ;EAOI,0CAAA;AhBysDZ;;AgBhtDQ;EAOI,wCAAA;AhB6sDZ;;AgBptDQ;EAOI,qCAAA;AhBitDZ;;AgBxtDQ;EAOI,wCAAA;AhBqtDZ;;AgB5tDQ;EAOI,uCAAA;AhBytDZ;;AgBhuDQ;EAOI,sCAAA;AhB6tDZ;;AgBpuDQ;EAOI,qCAAA;AhBiuDZ;;AgBxuDQ;EAIQ,kBAAA;EAGJ,8EAAA;AhBsuDZ;;AgB7uDQ;EAIQ,kBAAA;EAGJ,gFAAA;AhB2uDZ;;AgBlvDQ;EAIQ,kBAAA;EAGJ,8EAAA;AhBgvDZ;;AgBvvDQ;EAIQ,kBAAA;EAGJ,2EAAA;AhBqvDZ;;AgB5vDQ;EAIQ,kBAAA;EAGJ,8EAAA;AhB0vDZ;;AgBjwDQ;EAIQ,kBAAA;EAGJ,6EAAA;AhB+vDZ;;AgBtwDQ;EAIQ,kBAAA;EAGJ,4EAAA;AhBowDZ;;AgB3wDQ;EAIQ,kBAAA;EAGJ,2EAAA;AhBywDZ;;AgBhxDQ;EAIQ,kBAAA;EAGJ,4EAAA;AhB8wDZ;;AgBrxDQ;EAIQ,kBAAA;EAGJ,4EAAA;AhBmxDZ;;AgB1xDQ;EAIQ,kBAAA;EAGJ,8EAAA;AhBwxDZ;;AgB/xDQ;EAIQ,kBAAA;EAGJ,wCAAA;AhB6xDZ;;AgBpyDQ;EAIQ,kBAAA;EAGJ,mFAAA;AhBkyDZ;;AgBzyDQ;EAIQ,kBAAA;EAGJ,kFAAA;AhBuyDZ;;AgB9yDQ;EAIQ,kBAAA;EAGJ,kFAAA;AhB4yDZ;;AgB7zDQ;EACE,oBAAA;AhBg0DV;;AgBj0DQ;EACE,qBAAA;AhBo0DV;;AgBr0DQ;EACE,oBAAA;AhBw0DV;;AgBz0DQ;EACE,qBAAA;AhB40DV;;AgB70DQ;EACE,kBAAA;AhBg1DV;;AgBv0DQ;EAOI,wDAAA;AhBo0DZ;;AgB30DQ;EAOI,0DAAA;AhBw0DZ;;AgB/0DQ;EAOI,wDAAA;AhB40DZ;;AgBn1DQ;EAOI,qDAAA;AhBg1DZ;;AgBv1DQ;EAOI,wDAAA;AhBo1DZ;;AgB31DQ;EAOI,uDAAA;AhBw1DZ;;AgB/1DQ;EAOI,sDAAA;AhB41DZ;;AgBn2DQ;EAOI,qDAAA;AhBg2DZ;;AgBv2DQ;EAOI,+CAAA;AhBo2DZ;;AgB32DQ;EAOI,mCAAA;EAAA,gCAAA;EAAA,2BAAA;AhBw2DZ;;AgB/2DQ;EAOI,oCAAA;EAAA,iCAAA;EAAA,4BAAA;AhB42DZ;;AgBn3DQ;EAOI,oCAAA;EAAA,iCAAA;EAAA,4BAAA;AhBg3DZ;;AgBv3DQ;EAOI,+BAAA;AhBo3DZ;;AgB33DQ;EAOI,+BAAA;AhBw3DZ;;AgB/3DQ;EAOI,iDAAA;AhB43DZ;;AgBn4DQ;EAOI,2BAAA;AhBg4DZ;;AgBv4DQ;EAOI,oDAAA;AhBo4DZ;;AgB34DQ;EAOI,iDAAA;AhBw4DZ;;AgB/4DQ;EAOI,oDAAA;AhB44DZ;;AgBn5DQ;EAOI,oDAAA;AhBg5DZ;;AgBv5DQ;EAOI,qDAAA;AhBo5DZ;;AgB35DQ;EAOI,6BAAA;AhBw5DZ;;AgB/5DQ;EAOI,sDAAA;AhB45DZ;;AgBn6DQ;EAOI,0DAAA;EAAA,2DAAA;AhBi6DZ;;AgBx6DQ;EAOI,oCAAA;EAAA,qCAAA;AhBs6DZ;;AgB76DQ;EAOI,6DAAA;EAAA,8DAAA;AhB26DZ;;AgBl7DQ;EAOI,0DAAA;EAAA,2DAAA;AhBg7DZ;;AgBv7DQ;EAOI,6DAAA;EAAA,8DAAA;AhBq7DZ;;AgB57DQ;EAOI,6DAAA;EAAA,8DAAA;AhB07DZ;;AgBj8DQ;EAOI,8DAAA;EAAA,+DAAA;AhB+7DZ;;AgBt8DQ;EAOI,sCAAA;EAAA,uCAAA;AhBo8DZ;;AgB38DQ;EAOI,+DAAA;EAAA,gEAAA;AhBy8DZ;;AgBh9DQ;EAOI,2DAAA;EAAA,8DAAA;AhB88DZ;;AgBr9DQ;EAOI,qCAAA;EAAA,wCAAA;AhBm9DZ;;AgB19DQ;EAOI,8DAAA;EAAA,iEAAA;AhBw9DZ;;AgB/9DQ;EAOI,2DAAA;EAAA,8DAAA;AhB69DZ;;AgBp+DQ;EAOI,8DAAA;EAAA,iEAAA;AhBk+DZ;;AgBz+DQ;EAOI,8DAAA;EAAA,iEAAA;AhBu+DZ;;AgB9+DQ;EAOI,+DAAA;EAAA,kEAAA;AhB4+DZ;;AgBn/DQ;EAOI,uCAAA;EAAA,0CAAA;AhBi/DZ;;AgBx/DQ;EAOI,gEAAA;EAAA,mEAAA;AhBs/DZ;;AgB7/DQ;EAOI,8DAAA;EAAA,6DAAA;AhB2/DZ;;AgBlgEQ;EAOI,wCAAA;EAAA,uCAAA;AhBggEZ;;AgBvgEQ;EAOI,iEAAA;EAAA,gEAAA;AhBqgEZ;;AgB5gEQ;EAOI,8DAAA;EAAA,6DAAA;AhB0gEZ;;AgBjhEQ;EAOI,iEAAA;EAAA,gEAAA;AhB+gEZ;;AgBthEQ;EAOI,iEAAA;EAAA,gEAAA;AhBohEZ;;AgB3hEQ;EAOI,kEAAA;EAAA,iEAAA;AhByhEZ;;AgBhiEQ;EAOI,0CAAA;EAAA,yCAAA;AhB8hEZ;;AgBriEQ;EAOI,mEAAA;EAAA,kEAAA;AhBmiEZ;;AgB1iEQ;EAOI,6DAAA;EAAA,0DAAA;AhBwiEZ;;AgB/iEQ;EAOI,uCAAA;EAAA,oCAAA;AhB6iEZ;;AgBpjEQ;EAOI,gEAAA;EAAA,6DAAA;AhBkjEZ;;AgBzjEQ;EAOI,6DAAA;EAAA,0DAAA;AhBujEZ;;AgB9jEQ;EAOI,gEAAA;EAAA,6DAAA;AhB4jEZ;;AgBnkEQ;EAOI,gEAAA;EAAA,6DAAA;AhBikEZ;;AgBxkEQ;EAOI,iEAAA;EAAA,8DAAA;AhBskEZ;;AgB7kEQ;EAOI,yCAAA;EAAA,sCAAA;AhB2kEZ;;AgBllEQ;EAOI,kEAAA;EAAA,+DAAA;AhBglEZ;;AgBvlEQ;EAOI,8BAAA;AhBolEZ;;AgB3lEQ;EAOI,6BAAA;AhBwlEZ;;AgB/lEQ;EAOI,sBAAA;AhB4lEZ;;AgBnmEQ;EAOI,qBAAA;AhBgmEZ;;AgBvmEQ;EAOI,qBAAA;AhBomEZ;;AgB3mEQ;EAOI,qBAAA;AhBwmEZ;;AgB/mEQ;EAOI,qBAAA;AhB4mEZ;;AQtnEI;EQGI;IAOI,sBAAA;EhBinEV;EgBxnEM;IAOI,uBAAA;EhBonEV;EgB3nEM;IAOI,sBAAA;EhBunEV;EgB9nEM;IAOI,iCAAA;IAAA,8BAAA;EhB0nEV;EgBjoEM;IAOI,+BAAA;IAAA,4BAAA;EhB6nEV;EgBpoEM;IAOI,8BAAA;IAAA,2BAAA;EhBgoEV;EgBvoEM;IAOI,oCAAA;IAAA,iCAAA;EhBmoEV;EgB1oEM;IAOI,8BAAA;IAAA,2BAAA;EhBsoEV;EgB7oEM;IAOI,0BAAA;EhByoEV;EgBhpEM;IAOI,gCAAA;EhB4oEV;EgBnpEM;IAOI,yBAAA;EhB+oEV;EgBtpEM;IAOI,wBAAA;EhBkpEV;EgBzpEM;IAOI,yBAAA;EhBqpEV;EgB5pEM;IAOI,6BAAA;EhBwpEV;EgB/pEM;IAOI,8BAAA;EhB2pEV;EgBlqEM;IAOI,wBAAA;EhB8pEV;EgBrqEM;IAOI,+BAAA;EhBiqEV;EgBxqEM;IAOI,wBAAA;EhBoqEV;EgB3qEM;IAOI,yBAAA;EhBuqEV;EgB9qEM;IAOI,8BAAA;EhB0qEV;EgBjrEM;IAOI,iCAAA;EhB6qEV;EgBprEM;IAOI,sCAAA;EhBgrEV;EgBvrEM;IAOI,yCAAA;EhBmrEV;EgB1rEM;IAOI,uBAAA;EhBsrEV;EgB7rEM;IAOI,uBAAA;EhByrEV;EgBhsEM;IAOI,yBAAA;EhB4rEV;EgBnsEM;IAOI,yBAAA;EhB+rEV;EgBtsEM;IAOI,0BAAA;EhBksEV;EgBzsEM;IAOI,4BAAA;EhBqsEV;EgB5sEM;IAOI,kCAAA;EhBwsEV;EgB/sEM;IAOI,sCAAA;EhB2sEV;EgBltEM;IAOI,oCAAA;EhB8sEV;EgBrtEM;IAOI,kCAAA;EhBitEV;EgBxtEM;IAOI,yCAAA;EhBotEV;EgB3tEM;IAOI,wCAAA;EhButEV;EgB9tEM;IAOI,wCAAA;EhB0tEV;EgBjuEM;IAOI,kCAAA;EhB6tEV;EgBpuEM;IAOI,gCAAA;EhBguEV;EgBvuEM;IAOI,8BAAA;EhBmuEV;EgB1uEM;IAOI,gCAAA;EhBsuEV;EgB7uEM;IAOI,+BAAA;EhByuEV;EgBhvEM;IAOI,oCAAA;EhB4uEV;EgBnvEM;IAOI,kCAAA;EhB+uEV;EgBtvEM;IAOI,gCAAA;EhBkvEV;EgBzvEM;IAOI,uCAAA;EhBqvEV;EgB5vEM;IAOI,sCAAA;EhBwvEV;EgB/vEM;IAOI,iCAAA;EhB2vEV;EgBlwEM;IAOI,2BAAA;EhB8vEV;EgBrwEM;IAOI,iCAAA;EhBiwEV;EgBxwEM;IAOI,+BAAA;EhBowEV;EgB3wEM;IAOI,6BAAA;EhBuwEV;EgB9wEM;IAOI,+BAAA;EhB0wEV;EgBjxEM;IAOI,8BAAA;EhB6wEV;EgBpxEM;IAOI,oBAAA;EhBgxEV;EgBvxEM;IAOI,mBAAA;EhBmxEV;EgB1xEM;IAOI,mBAAA;EhBsxEV;EgB7xEM;IAOI,mBAAA;EhByxEV;EgBhyEM;IAOI,mBAAA;EhB4xEV;EgBnyEM;IAOI,mBAAA;EhB+xEV;EgBtyEM;IAOI,mBAAA;EhBkyEV;EgBzyEM;IAOI,mBAAA;EhBqyEV;EgB5yEM;IAOI,oBAAA;EhBwyEV;EgB/yEM;IAOI,0BAAA;EhB2yEV;EgBlzEM;IAOI,yBAAA;EhB8yEV;EgBrzEM;IAOI,uBAAA;EhBizEV;EgBxzEM;IAOI,yBAAA;EhBozEV;EgB3zEM;IAOI,uBAAA;EhBuzEV;EgB9zEM;IAOI,uBAAA;EhB0zEV;EgBj0EM;IAOI,0BAAA;IAAA,yBAAA;EhB8zEV;EgBr0EM;IAOI,gCAAA;IAAA,+BAAA;EhBk0EV;EgBz0EM;IAOI,+BAAA;IAAA,8BAAA;EhBs0EV;EgB70EM;IAOI,6BAAA;IAAA,4BAAA;EhB00EV;EgBj1EM;IAOI,+BAAA;IAAA,8BAAA;EhB80EV;EgBr1EM;IAOI,6BAAA;IAAA,4BAAA;EhBk1EV;EgBz1EM;IAOI,6BAAA;IAAA,4BAAA;EhBs1EV;EgB71EM;IAOI,wBAAA;IAAA,2BAAA;EhB01EV;EgBj2EM;IAOI,8BAAA;IAAA,iCAAA;EhB81EV;EgBr2EM;IAOI,6BAAA;IAAA,gCAAA;EhBk2EV;EgBz2EM;IAOI,2BAAA;IAAA,8BAAA;EhBs2EV;EgB72EM;IAOI,6BAAA;IAAA,gCAAA;EhB02EV;EgBj3EM;IAOI,2BAAA;IAAA,8BAAA;EhB82EV;EgBr3EM;IAOI,2BAAA;IAAA,8BAAA;EhBk3EV;EgBz3EM;IAOI,wBAAA;EhBq3EV;EgB53EM;IAOI,8BAAA;EhBw3EV;EgB/3EM;IAOI,6BAAA;EhB23EV;EgBl4EM;IAOI,2BAAA;EhB83EV;EgBr4EM;IAOI,6BAAA;EhBi4EV;EgBx4EM;IAOI,2BAAA;EhBo4EV;EgB34EM;IAOI,2BAAA;EhBu4EV;EgB94EM;IAOI,0BAAA;EhB04EV;EgBj5EM;IAOI,gCAAA;EhB64EV;EgBp5EM;IAOI,+BAAA;EhBg5EV;EgBv5EM;IAOI,6BAAA;EhBm5EV;EgB15EM;IAOI,+BAAA;EhBs5EV;EgB75EM;IAOI,6BAAA;EhBy5EV;EgBh6EM;IAOI,6BAAA;EhB45EV;EgBn6EM;IAOI,2BAAA;EhB+5EV;EgBt6EM;IAOI,iCAAA;EhBk6EV;EgBz6EM;IAOI,gCAAA;EhBq6EV;EgB56EM;IAOI,8BAAA;EhBw6EV;EgB/6EM;IAOI,gCAAA;EhB26EV;EgBl7EM;IAOI,8BAAA;EhB86EV;EgBr7EM;IAOI,8BAAA;EhBi7EV;EgBx7EM;IAOI,yBAAA;EhBo7EV;EgB37EM;IAOI,+BAAA;EhBu7EV;EgB97EM;IAOI,8BAAA;EhB07EV;EgBj8EM;IAOI,4BAAA;EhB67EV;EgBp8EM;IAOI,8BAAA;EhBg8EV;EgBv8EM;IAOI,4BAAA;EhBm8EV;EgB18EM;IAOI,4BAAA;EhBs8EV;EgB78EM;IAOI,qBAAA;EhBy8EV;EgBh9EM;IAOI,2BAAA;EhB48EV;EgBn9EM;IAOI,0BAAA;EhB+8EV;EgBt9EM;IAOI,wBAAA;EhBk9EV;EgBz9EM;IAOI,0BAAA;EhBq9EV;EgB59EM;IAOI,wBAAA;EhBw9EV;EgB/9EM;IAOI,2BAAA;IAAA,0BAAA;EhB49EV;EgBn+EM;IAOI,iCAAA;IAAA,gCAAA;EhBg+EV;EgBv+EM;IAOI,gCAAA;IAAA,+BAAA;EhBo+EV;EgB3+EM;IAOI,8BAAA;IAAA,6BAAA;EhBw+EV;EgB/+EM;IAOI,gCAAA;IAAA,+BAAA;EhB4+EV;EgBn/EM;IAOI,8BAAA;IAAA,6BAAA;EhBg/EV;EgBv/EM;IAOI,yBAAA;IAAA,4BAAA;EhBo/EV;EgB3/EM;IAOI,+BAAA;IAAA,kCAAA;EhBw/EV;EgB//EM;IAOI,8BAAA;IAAA,iCAAA;EhB4/EV;EgBngFM;IAOI,4BAAA;IAAA,+BAAA;EhBggFV;EgBvgFM;IAOI,8BAAA;IAAA,iCAAA;EhBogFV;EgB3gFM;IAOI,4BAAA;IAAA,+BAAA;EhBwgFV;EgB/gFM;IAOI,yBAAA;EhB2gFV;EgBlhFM;IAOI,+BAAA;EhB8gFV;EgBrhFM;IAOI,8BAAA;EhBihFV;EgBxhFM;IAOI,4BAAA;EhBohFV;EgB3hFM;IAOI,8BAAA;EhBuhFV;EgB9hFM;IAOI,4BAAA;EhB0hFV;EgBjiFM;IAOI,2BAAA;EhB6hFV;EgBpiFM;IAOI,iCAAA;EhBgiFV;EgBviFM;IAOI,gCAAA;EhBmiFV;EgB1iFM;IAOI,8BAAA;EhBsiFV;EgB7iFM;IAOI,gCAAA;EhByiFV;EgBhjFM;IAOI,8BAAA;EhB4iFV;EgBnjFM;IAOI,4BAAA;EhB+iFV;EgBtjFM;IAOI,kCAAA;EhBkjFV;EgBzjFM;IAOI,iCAAA;EhBqjFV;EgB5jFM;IAOI,+BAAA;EhBwjFV;EgB/jFM;IAOI,iCAAA;EhB2jFV;EgBlkFM;IAOI,+BAAA;EhB8jFV;EgBrkFM;IAOI,0BAAA;EhBikFV;EgBxkFM;IAOI,gCAAA;EhBokFV;EgB3kFM;IAOI,+BAAA;EhBukFV;EgB9kFM;IAOI,6BAAA;EhB0kFV;EgBjlFM;IAOI,+BAAA;EhB6kFV;EgBplFM;IAOI,6BAAA;EhBglFV;EgBvlFM;IAOI,iBAAA;EhBmlFV;EgB1lFM;IAOI,uBAAA;EhBslFV;EgB7lFM;IAOI,sBAAA;EhBylFV;EgBhmFM;IAOI,oBAAA;EhB4lFV;EgBnmFM;IAOI,sBAAA;EhB+lFV;EgBtmFM;IAOI,oBAAA;EhBkmFV;EgBzmFM;IAOI,qBAAA;EhBqmFV;EgB5mFM;IAOI,2BAAA;EhBwmFV;EgB/mFM;IAOI,0BAAA;EhB2mFV;EgBlnFM;IAOI,wBAAA;EhB8mFV;EgBrnFM;IAOI,0BAAA;EhBinFV;EgBxnFM;IAOI,wBAAA;EhBonFV;EgB3nFM;IAOI,6BAAA;IAAA,wBAAA;EhBunFV;EgB9nFM;IAOI,mCAAA;IAAA,8BAAA;EhB0nFV;EgBjoFM;IAOI,kCAAA;IAAA,6BAAA;EhB6nFV;EgBpoFM;IAOI,gCAAA;IAAA,2BAAA;EhBgoFV;EgBvoFM;IAOI,kCAAA;IAAA,6BAAA;EhBmoFV;EgB1oFM;IAOI,gCAAA;IAAA,2BAAA;EhBsoFV;EgB7oFM;IAOI,2BAAA;EhByoFV;EgBhpFM;IAOI,4BAAA;EhB4oFV;EgBnpFM;IAOI,6BAAA;EhB+oFV;AACF;AQ1pFI;EQGI;IAOI,sBAAA;EhBopFV;EgB3pFM;IAOI,uBAAA;EhBupFV;EgB9pFM;IAOI,sBAAA;EhB0pFV;EgBjqFM;IAOI,iCAAA;IAAA,8BAAA;EhB6pFV;EgBpqFM;IAOI,+BAAA;IAAA,4BAAA;EhBgqFV;EgBvqFM;IAOI,8BAAA;IAAA,2BAAA;EhBmqFV;EgB1qFM;IAOI,oCAAA;IAAA,iCAAA;EhBsqFV;EgB7qFM;IAOI,8BAAA;IAAA,2BAAA;EhByqFV;EgBhrFM;IAOI,0BAAA;EhB4qFV;EgBnrFM;IAOI,gCAAA;EhB+qFV;EgBtrFM;IAOI,yBAAA;EhBkrFV;EgBzrFM;IAOI,wBAAA;EhBqrFV;EgB5rFM;IAOI,yBAAA;EhBwrFV;EgB/rFM;IAOI,6BAAA;EhB2rFV;EgBlsFM;IAOI,8BAAA;EhB8rFV;EgBrsFM;IAOI,wBAAA;EhBisFV;EgBxsFM;IAOI,+BAAA;EhBosFV;EgB3sFM;IAOI,wBAAA;EhBusFV;EgB9sFM;IAOI,yBAAA;EhB0sFV;EgBjtFM;IAOI,8BAAA;EhB6sFV;EgBptFM;IAOI,iCAAA;EhBgtFV;EgBvtFM;IAOI,sCAAA;EhBmtFV;EgB1tFM;IAOI,yCAAA;EhBstFV;EgB7tFM;IAOI,uBAAA;EhBytFV;EgBhuFM;IAOI,uBAAA;EhB4tFV;EgBnuFM;IAOI,yBAAA;EhB+tFV;EgBtuFM;IAOI,yBAAA;EhBkuFV;EgBzuFM;IAOI,0BAAA;EhBquFV;EgB5uFM;IAOI,4BAAA;EhBwuFV;EgB/uFM;IAOI,kCAAA;EhB2uFV;EgBlvFM;IAOI,sCAAA;EhB8uFV;EgBrvFM;IAOI,oCAAA;EhBivFV;EgBxvFM;IAOI,kCAAA;EhBovFV;EgB3vFM;IAOI,yCAAA;EhBuvFV;EgB9vFM;IAOI,wCAAA;EhB0vFV;EgBjwFM;IAOI,wCAAA;EhB6vFV;EgBpwFM;IAOI,kCAAA;EhBgwFV;EgBvwFM;IAOI,gCAAA;EhBmwFV;EgB1wFM;IAOI,8BAAA;EhBswFV;EgB7wFM;IAOI,gCAAA;EhBywFV;EgBhxFM;IAOI,+BAAA;EhB4wFV;EgBnxFM;IAOI,oCAAA;EhB+wFV;EgBtxFM;IAOI,kCAAA;EhBkxFV;EgBzxFM;IAOI,gCAAA;EhBqxFV;EgB5xFM;IAOI,uCAAA;EhBwxFV;EgB/xFM;IAOI,sCAAA;EhB2xFV;EgBlyFM;IAOI,iCAAA;EhB8xFV;EgBryFM;IAOI,2BAAA;EhBiyFV;EgBxyFM;IAOI,iCAAA;EhBoyFV;EgB3yFM;IAOI,+BAAA;EhBuyFV;EgB9yFM;IAOI,6BAAA;EhB0yFV;EgBjzFM;IAOI,+BAAA;EhB6yFV;EgBpzFM;IAOI,8BAAA;EhBgzFV;EgBvzFM;IAOI,oBAAA;EhBmzFV;EgB1zFM;IAOI,mBAAA;EhBszFV;EgB7zFM;IAOI,mBAAA;EhByzFV;EgBh0FM;IAOI,mBAAA;EhB4zFV;EgBn0FM;IAOI,mBAAA;EhB+zFV;EgBt0FM;IAOI,mBAAA;EhBk0FV;EgBz0FM;IAOI,mBAAA;EhBq0FV;EgB50FM;IAOI,mBAAA;EhBw0FV;EgB/0FM;IAOI,oBAAA;EhB20FV;EgBl1FM;IAOI,0BAAA;EhB80FV;EgBr1FM;IAOI,yBAAA;EhBi1FV;EgBx1FM;IAOI,uBAAA;EhBo1FV;EgB31FM;IAOI,yBAAA;EhBu1FV;EgB91FM;IAOI,uBAAA;EhB01FV;EgBj2FM;IAOI,uBAAA;EhB61FV;EgBp2FM;IAOI,0BAAA;IAAA,yBAAA;EhBi2FV;EgBx2FM;IAOI,gCAAA;IAAA,+BAAA;EhBq2FV;EgB52FM;IAOI,+BAAA;IAAA,8BAAA;EhBy2FV;EgBh3FM;IAOI,6BAAA;IAAA,4BAAA;EhB62FV;EgBp3FM;IAOI,+BAAA;IAAA,8BAAA;EhBi3FV;EgBx3FM;IAOI,6BAAA;IAAA,4BAAA;EhBq3FV;EgB53FM;IAOI,6BAAA;IAAA,4BAAA;EhBy3FV;EgBh4FM;IAOI,wBAAA;IAAA,2BAAA;EhB63FV;EgBp4FM;IAOI,8BAAA;IAAA,iCAAA;EhBi4FV;EgBx4FM;IAOI,6BAAA;IAAA,gCAAA;EhBq4FV;EgB54FM;IAOI,2BAAA;IAAA,8BAAA;EhBy4FV;EgBh5FM;IAOI,6BAAA;IAAA,gCAAA;EhB64FV;EgBp5FM;IAOI,2BAAA;IAAA,8BAAA;EhBi5FV;EgBx5FM;IAOI,2BAAA;IAAA,8BAAA;EhBq5FV;EgB55FM;IAOI,wBAAA;EhBw5FV;EgB/5FM;IAOI,8BAAA;EhB25FV;EgBl6FM;IAOI,6BAAA;EhB85FV;EgBr6FM;IAOI,2BAAA;EhBi6FV;EgBx6FM;IAOI,6BAAA;EhBo6FV;EgB36FM;IAOI,2BAAA;EhBu6FV;EgB96FM;IAOI,2BAAA;EhB06FV;EgBj7FM;IAOI,0BAAA;EhB66FV;EgBp7FM;IAOI,gCAAA;EhBg7FV;EgBv7FM;IAOI,+BAAA;EhBm7FV;EgB17FM;IAOI,6BAAA;EhBs7FV;EgB77FM;IAOI,+BAAA;EhBy7FV;EgBh8FM;IAOI,6BAAA;EhB47FV;EgBn8FM;IAOI,6BAAA;EhB+7FV;EgBt8FM;IAOI,2BAAA;EhBk8FV;EgBz8FM;IAOI,iCAAA;EhBq8FV;EgB58FM;IAOI,gCAAA;EhBw8FV;EgB/8FM;IAOI,8BAAA;EhB28FV;EgBl9FM;IAOI,gCAAA;EhB88FV;EgBr9FM;IAOI,8BAAA;EhBi9FV;EgBx9FM;IAOI,8BAAA;EhBo9FV;EgB39FM;IAOI,yBAAA;EhBu9FV;EgB99FM;IAOI,+BAAA;EhB09FV;EgBj+FM;IAOI,8BAAA;EhB69FV;EgBp+FM;IAOI,4BAAA;EhBg+FV;EgBv+FM;IAOI,8BAAA;EhBm+FV;EgB1+FM;IAOI,4BAAA;EhBs+FV;EgB7+FM;IAOI,4BAAA;EhBy+FV;EgBh/FM;IAOI,qBAAA;EhB4+FV;EgBn/FM;IAOI,2BAAA;EhB++FV;EgBt/FM;IAOI,0BAAA;EhBk/FV;EgBz/FM;IAOI,wBAAA;EhBq/FV;EgB5/FM;IAOI,0BAAA;EhBw/FV;EgB//FM;IAOI,wBAAA;EhB2/FV;EgBlgGM;IAOI,2BAAA;IAAA,0BAAA;EhB+/FV;EgBtgGM;IAOI,iCAAA;IAAA,gCAAA;EhBmgGV;EgB1gGM;IAOI,gCAAA;IAAA,+BAAA;EhBugGV;EgB9gGM;IAOI,8BAAA;IAAA,6BAAA;EhB2gGV;EgBlhGM;IAOI,gCAAA;IAAA,+BAAA;EhB+gGV;EgBthGM;IAOI,8BAAA;IAAA,6BAAA;EhBmhGV;EgB1hGM;IAOI,yBAAA;IAAA,4BAAA;EhBuhGV;EgB9hGM;IAOI,+BAAA;IAAA,kCAAA;EhB2hGV;EgBliGM;IAOI,8BAAA;IAAA,iCAAA;EhB+hGV;EgBtiGM;IAOI,4BAAA;IAAA,+BAAA;EhBmiGV;EgB1iGM;IAOI,8BAAA;IAAA,iCAAA;EhBuiGV;EgB9iGM;IAOI,4BAAA;IAAA,+BAAA;EhB2iGV;EgBljGM;IAOI,yBAAA;EhB8iGV;EgBrjGM;IAOI,+BAAA;EhBijGV;EgBxjGM;IAOI,8BAAA;EhBojGV;EgB3jGM;IAOI,4BAAA;EhBujGV;EgB9jGM;IAOI,8BAAA;EhB0jGV;EgBjkGM;IAOI,4BAAA;EhB6jGV;EgBpkGM;IAOI,2BAAA;EhBgkGV;EgBvkGM;IAOI,iCAAA;EhBmkGV;EgB1kGM;IAOI,gCAAA;EhBskGV;EgB7kGM;IAOI,8BAAA;EhBykGV;EgBhlGM;IAOI,gCAAA;EhB4kGV;EgBnlGM;IAOI,8BAAA;EhB+kGV;EgBtlGM;IAOI,4BAAA;EhBklGV;EgBzlGM;IAOI,kCAAA;EhBqlGV;EgB5lGM;IAOI,iCAAA;EhBwlGV;EgB/lGM;IAOI,+BAAA;EhB2lGV;EgBlmGM;IAOI,iCAAA;EhB8lGV;EgBrmGM;IAOI,+BAAA;EhBimGV;EgBxmGM;IAOI,0BAAA;EhBomGV;EgB3mGM;IAOI,gCAAA;EhBumGV;EgB9mGM;IAOI,+BAAA;EhB0mGV;EgBjnGM;IAOI,6BAAA;EhB6mGV;EgBpnGM;IAOI,+BAAA;EhBgnGV;EgBvnGM;IAOI,6BAAA;EhBmnGV;EgB1nGM;IAOI,iBAAA;EhBsnGV;EgB7nGM;IAOI,uBAAA;EhBynGV;EgBhoGM;IAOI,sBAAA;EhB4nGV;EgBnoGM;IAOI,oBAAA;EhB+nGV;EgBtoGM;IAOI,sBAAA;EhBkoGV;EgBzoGM;IAOI,oBAAA;EhBqoGV;EgB5oGM;IAOI,qBAAA;EhBwoGV;EgB/oGM;IAOI,2BAAA;EhB2oGV;EgBlpGM;IAOI,0BAAA;EhB8oGV;EgBrpGM;IAOI,wBAAA;EhBipGV;EgBxpGM;IAOI,0BAAA;EhBopGV;EgB3pGM;IAOI,wBAAA;EhBupGV;EgB9pGM;IAOI,6BAAA;IAAA,wBAAA;EhB0pGV;EgBjqGM;IAOI,mCAAA;IAAA,8BAAA;EhB6pGV;EgBpqGM;IAOI,kCAAA;IAAA,6BAAA;EhBgqGV;EgBvqGM;IAOI,gCAAA;IAAA,2BAAA;EhBmqGV;EgB1qGM;IAOI,kCAAA;IAAA,6BAAA;EhBsqGV;EgB7qGM;IAOI,gCAAA;IAAA,2BAAA;EhByqGV;EgBhrGM;IAOI,2BAAA;EhB4qGV;EgBnrGM;IAOI,4BAAA;EhB+qGV;EgBtrGM;IAOI,6BAAA;EhBkrGV;AACF;AQ7rGI;EQGI;IAOI,sBAAA;EhBurGV;EgB9rGM;IAOI,uBAAA;EhB0rGV;EgBjsGM;IAOI,sBAAA;EhB6rGV;EgBpsGM;IAOI,iCAAA;IAAA,8BAAA;EhBgsGV;EgBvsGM;IAOI,+BAAA;IAAA,4BAAA;EhBmsGV;EgB1sGM;IAOI,8BAAA;IAAA,2BAAA;EhBssGV;EgB7sGM;IAOI,oCAAA;IAAA,iCAAA;EhBysGV;EgBhtGM;IAOI,8BAAA;IAAA,2BAAA;EhB4sGV;EgBntGM;IAOI,0BAAA;EhB+sGV;EgBttGM;IAOI,gCAAA;EhBktGV;EgBztGM;IAOI,yBAAA;EhBqtGV;EgB5tGM;IAOI,wBAAA;EhBwtGV;EgB/tGM;IAOI,yBAAA;EhB2tGV;EgBluGM;IAOI,6BAAA;EhB8tGV;EgBruGM;IAOI,8BAAA;EhBiuGV;EgBxuGM;IAOI,wBAAA;EhBouGV;EgB3uGM;IAOI,+BAAA;EhBuuGV;EgB9uGM;IAOI,wBAAA;EhB0uGV;EgBjvGM;IAOI,yBAAA;EhB6uGV;EgBpvGM;IAOI,8BAAA;EhBgvGV;EgBvvGM;IAOI,iCAAA;EhBmvGV;EgB1vGM;IAOI,sCAAA;EhBsvGV;EgB7vGM;IAOI,yCAAA;EhByvGV;EgBhwGM;IAOI,uBAAA;EhB4vGV;EgBnwGM;IAOI,uBAAA;EhB+vGV;EgBtwGM;IAOI,yBAAA;EhBkwGV;EgBzwGM;IAOI,yBAAA;EhBqwGV;EgB5wGM;IAOI,0BAAA;EhBwwGV;EgB/wGM;IAOI,4BAAA;EhB2wGV;EgBlxGM;IAOI,kCAAA;EhB8wGV;EgBrxGM;IAOI,sCAAA;EhBixGV;EgBxxGM;IAOI,oCAAA;EhBoxGV;EgB3xGM;IAOI,kCAAA;EhBuxGV;EgB9xGM;IAOI,yCAAA;EhB0xGV;EgBjyGM;IAOI,wCAAA;EhB6xGV;EgBpyGM;IAOI,wCAAA;EhBgyGV;EgBvyGM;IAOI,kCAAA;EhBmyGV;EgB1yGM;IAOI,gCAAA;EhBsyGV;EgB7yGM;IAOI,8BAAA;EhByyGV;EgBhzGM;IAOI,gCAAA;EhB4yGV;EgBnzGM;IAOI,+BAAA;EhB+yGV;EgBtzGM;IAOI,oCAAA;EhBkzGV;EgBzzGM;IAOI,kCAAA;EhBqzGV;EgB5zGM;IAOI,gCAAA;EhBwzGV;EgB/zGM;IAOI,uCAAA;EhB2zGV;EgBl0GM;IAOI,sCAAA;EhB8zGV;EgBr0GM;IAOI,iCAAA;EhBi0GV;EgBx0GM;IAOI,2BAAA;EhBo0GV;EgB30GM;IAOI,iCAAA;EhBu0GV;EgB90GM;IAOI,+BAAA;EhB00GV;EgBj1GM;IAOI,6BAAA;EhB60GV;EgBp1GM;IAOI,+BAAA;EhBg1GV;EgBv1GM;IAOI,8BAAA;EhBm1GV;EgB11GM;IAOI,oBAAA;EhBs1GV;EgB71GM;IAOI,mBAAA;EhBy1GV;EgBh2GM;IAOI,mBAAA;EhB41GV;EgBn2GM;IAOI,mBAAA;EhB+1GV;EgBt2GM;IAOI,mBAAA;EhBk2GV;EgBz2GM;IAOI,mBAAA;EhBq2GV;EgB52GM;IAOI,mBAAA;EhBw2GV;EgB/2GM;IAOI,mBAAA;EhB22GV;EgBl3GM;IAOI,oBAAA;EhB82GV;EgBr3GM;IAOI,0BAAA;EhBi3GV;EgBx3GM;IAOI,yBAAA;EhBo3GV;EgB33GM;IAOI,uBAAA;EhBu3GV;EgB93GM;IAOI,yBAAA;EhB03GV;EgBj4GM;IAOI,uBAAA;EhB63GV;EgBp4GM;IAOI,uBAAA;EhBg4GV;EgBv4GM;IAOI,0BAAA;IAAA,yBAAA;EhBo4GV;EgB34GM;IAOI,gCAAA;IAAA,+BAAA;EhBw4GV;EgB/4GM;IAOI,+BAAA;IAAA,8BAAA;EhB44GV;EgBn5GM;IAOI,6BAAA;IAAA,4BAAA;EhBg5GV;EgBv5GM;IAOI,+BAAA;IAAA,8BAAA;EhBo5GV;EgB35GM;IAOI,6BAAA;IAAA,4BAAA;EhBw5GV;EgB/5GM;IAOI,6BAAA;IAAA,4BAAA;EhB45GV;EgBn6GM;IAOI,wBAAA;IAAA,2BAAA;EhBg6GV;EgBv6GM;IAOI,8BAAA;IAAA,iCAAA;EhBo6GV;EgB36GM;IAOI,6BAAA;IAAA,gCAAA;EhBw6GV;EgB/6GM;IAOI,2BAAA;IAAA,8BAAA;EhB46GV;EgBn7GM;IAOI,6BAAA;IAAA,gCAAA;EhBg7GV;EgBv7GM;IAOI,2BAAA;IAAA,8BAAA;EhBo7GV;EgB37GM;IAOI,2BAAA;IAAA,8BAAA;EhBw7GV;EgB/7GM;IAOI,wBAAA;EhB27GV;EgBl8GM;IAOI,8BAAA;EhB87GV;EgBr8GM;IAOI,6BAAA;EhBi8GV;EgBx8GM;IAOI,2BAAA;EhBo8GV;EgB38GM;IAOI,6BAAA;EhBu8GV;EgB98GM;IAOI,2BAAA;EhB08GV;EgBj9GM;IAOI,2BAAA;EhB68GV;EgBp9GM;IAOI,0BAAA;EhBg9GV;EgBv9GM;IAOI,gCAAA;EhBm9GV;EgB19GM;IAOI,+BAAA;EhBs9GV;EgB79GM;IAOI,6BAAA;EhBy9GV;EgBh+GM;IAOI,+BAAA;EhB49GV;EgBn+GM;IAOI,6BAAA;EhB+9GV;EgBt+GM;IAOI,6BAAA;EhBk+GV;EgBz+GM;IAOI,2BAAA;EhBq+GV;EgB5+GM;IAOI,iCAAA;EhBw+GV;EgB/+GM;IAOI,gCAAA;EhB2+GV;EgBl/GM;IAOI,8BAAA;EhB8+GV;EgBr/GM;IAOI,gCAAA;EhBi/GV;EgBx/GM;IAOI,8BAAA;EhBo/GV;EgB3/GM;IAOI,8BAAA;EhBu/GV;EgB9/GM;IAOI,yBAAA;EhB0/GV;EgBjgHM;IAOI,+BAAA;EhB6/GV;EgBpgHM;IAOI,8BAAA;EhBggHV;EgBvgHM;IAOI,4BAAA;EhBmgHV;EgB1gHM;IAOI,8BAAA;EhBsgHV;EgB7gHM;IAOI,4BAAA;EhBygHV;EgBhhHM;IAOI,4BAAA;EhB4gHV;EgBnhHM;IAOI,qBAAA;EhB+gHV;EgBthHM;IAOI,2BAAA;EhBkhHV;EgBzhHM;IAOI,0BAAA;EhBqhHV;EgB5hHM;IAOI,wBAAA;EhBwhHV;EgB/hHM;IAOI,0BAAA;EhB2hHV;EgBliHM;IAOI,wBAAA;EhB8hHV;EgBriHM;IAOI,2BAAA;IAAA,0BAAA;EhBkiHV;EgBziHM;IAOI,iCAAA;IAAA,gCAAA;EhBsiHV;EgB7iHM;IAOI,gCAAA;IAAA,+BAAA;EhB0iHV;EgBjjHM;IAOI,8BAAA;IAAA,6BAAA;EhB8iHV;EgBrjHM;IAOI,gCAAA;IAAA,+BAAA;EhBkjHV;EgBzjHM;IAOI,8BAAA;IAAA,6BAAA;EhBsjHV;EgB7jHM;IAOI,yBAAA;IAAA,4BAAA;EhB0jHV;EgBjkHM;IAOI,+BAAA;IAAA,kCAAA;EhB8jHV;EgBrkHM;IAOI,8BAAA;IAAA,iCAAA;EhBkkHV;EgBzkHM;IAOI,4BAAA;IAAA,+BAAA;EhBskHV;EgB7kHM;IAOI,8BAAA;IAAA,iCAAA;EhB0kHV;EgBjlHM;IAOI,4BAAA;IAAA,+BAAA;EhB8kHV;EgBrlHM;IAOI,yBAAA;EhBilHV;EgBxlHM;IAOI,+BAAA;EhBolHV;EgB3lHM;IAOI,8BAAA;EhBulHV;EgB9lHM;IAOI,4BAAA;EhB0lHV;EgBjmHM;IAOI,8BAAA;EhB6lHV;EgBpmHM;IAOI,4BAAA;EhBgmHV;EgBvmHM;IAOI,2BAAA;EhBmmHV;EgB1mHM;IAOI,iCAAA;EhBsmHV;EgB7mHM;IAOI,gCAAA;EhBymHV;EgBhnHM;IAOI,8BAAA;EhB4mHV;EgBnnHM;IAOI,gCAAA;EhB+mHV;EgBtnHM;IAOI,8BAAA;EhBknHV;EgBznHM;IAOI,4BAAA;EhBqnHV;EgB5nHM;IAOI,kCAAA;EhBwnHV;EgB/nHM;IAOI,iCAAA;EhB2nHV;EgBloHM;IAOI,+BAAA;EhB8nHV;EgBroHM;IAOI,iCAAA;EhBioHV;EgBxoHM;IAOI,+BAAA;EhBooHV;EgB3oHM;IAOI,0BAAA;EhBuoHV;EgB9oHM;IAOI,gCAAA;EhB0oHV;EgBjpHM;IAOI,+BAAA;EhB6oHV;EgBppHM;IAOI,6BAAA;EhBgpHV;EgBvpHM;IAOI,+BAAA;EhBmpHV;EgB1pHM;IAOI,6BAAA;EhBspHV;EgB7pHM;IAOI,iBAAA;EhBypHV;EgBhqHM;IAOI,uBAAA;EhB4pHV;EgBnqHM;IAOI,sBAAA;EhB+pHV;EgBtqHM;IAOI,oBAAA;EhBkqHV;EgBzqHM;IAOI,sBAAA;EhBqqHV;EgB5qHM;IAOI,oBAAA;EhBwqHV;EgB/qHM;IAOI,qBAAA;EhB2qHV;EgBlrHM;IAOI,2BAAA;EhB8qHV;EgBrrHM;IAOI,0BAAA;EhBirHV;EgBxrHM;IAOI,wBAAA;EhBorHV;EgB3rHM;IAOI,0BAAA;EhBurHV;EgB9rHM;IAOI,wBAAA;EhB0rHV;EgBjsHM;IAOI,6BAAA;IAAA,wBAAA;EhB6rHV;EgBpsHM;IAOI,mCAAA;IAAA,8BAAA;EhBgsHV;EgBvsHM;IAOI,kCAAA;IAAA,6BAAA;EhBmsHV;EgB1sHM;IAOI,gCAAA;IAAA,2BAAA;EhBssHV;EgB7sHM;IAOI,kCAAA;IAAA,6BAAA;EhBysHV;EgBhtHM;IAOI,gCAAA;IAAA,2BAAA;EhB4sHV;EgBntHM;IAOI,2BAAA;EhB+sHV;EgBttHM;IAOI,4BAAA;EhBktHV;EgBztHM;IAOI,6BAAA;EhBqtHV;AACF;AQhuHI;EQGI;IAOI,sBAAA;EhB0tHV;EgBjuHM;IAOI,uBAAA;EhB6tHV;EgBpuHM;IAOI,sBAAA;EhBguHV;EgBvuHM;IAOI,iCAAA;IAAA,8BAAA;EhBmuHV;EgB1uHM;IAOI,+BAAA;IAAA,4BAAA;EhBsuHV;EgB7uHM;IAOI,8BAAA;IAAA,2BAAA;EhByuHV;EgBhvHM;IAOI,oCAAA;IAAA,iCAAA;EhB4uHV;EgBnvHM;IAOI,8BAAA;IAAA,2BAAA;EhB+uHV;EgBtvHM;IAOI,0BAAA;EhBkvHV;EgBzvHM;IAOI,gCAAA;EhBqvHV;EgB5vHM;IAOI,yBAAA;EhBwvHV;EgB/vHM;IAOI,wBAAA;EhB2vHV;EgBlwHM;IAOI,yBAAA;EhB8vHV;EgBrwHM;IAOI,6BAAA;EhBiwHV;EgBxwHM;IAOI,8BAAA;EhBowHV;EgB3wHM;IAOI,wBAAA;EhBuwHV;EgB9wHM;IAOI,+BAAA;EhB0wHV;EgBjxHM;IAOI,wBAAA;EhB6wHV;EgBpxHM;IAOI,yBAAA;EhBgxHV;EgBvxHM;IAOI,8BAAA;EhBmxHV;EgB1xHM;IAOI,iCAAA;EhBsxHV;EgB7xHM;IAOI,sCAAA;EhByxHV;EgBhyHM;IAOI,yCAAA;EhB4xHV;EgBnyHM;IAOI,uBAAA;EhB+xHV;EgBtyHM;IAOI,uBAAA;EhBkyHV;EgBzyHM;IAOI,yBAAA;EhBqyHV;EgB5yHM;IAOI,yBAAA;EhBwyHV;EgB/yHM;IAOI,0BAAA;EhB2yHV;EgBlzHM;IAOI,4BAAA;EhB8yHV;EgBrzHM;IAOI,kCAAA;EhBizHV;EgBxzHM;IAOI,sCAAA;EhBozHV;EgB3zHM;IAOI,oCAAA;EhBuzHV;EgB9zHM;IAOI,kCAAA;EhB0zHV;EgBj0HM;IAOI,yCAAA;EhB6zHV;EgBp0HM;IAOI,wCAAA;EhBg0HV;EgBv0HM;IAOI,wCAAA;EhBm0HV;EgB10HM;IAOI,kCAAA;EhBs0HV;EgB70HM;IAOI,gCAAA;EhBy0HV;EgBh1HM;IAOI,8BAAA;EhB40HV;EgBn1HM;IAOI,gCAAA;EhB+0HV;EgBt1HM;IAOI,+BAAA;EhBk1HV;EgBz1HM;IAOI,oCAAA;EhBq1HV;EgB51HM;IAOI,kCAAA;EhBw1HV;EgB/1HM;IAOI,gCAAA;EhB21HV;EgBl2HM;IAOI,uCAAA;EhB81HV;EgBr2HM;IAOI,sCAAA;EhBi2HV;EgBx2HM;IAOI,iCAAA;EhBo2HV;EgB32HM;IAOI,2BAAA;EhBu2HV;EgB92HM;IAOI,iCAAA;EhB02HV;EgBj3HM;IAOI,+BAAA;EhB62HV;EgBp3HM;IAOI,6BAAA;EhBg3HV;EgBv3HM;IAOI,+BAAA;EhBm3HV;EgB13HM;IAOI,8BAAA;EhBs3HV;EgB73HM;IAOI,oBAAA;EhBy3HV;EgBh4HM;IAOI,mBAAA;EhB43HV;EgBn4HM;IAOI,mBAAA;EhB+3HV;EgBt4HM;IAOI,mBAAA;EhBk4HV;EgBz4HM;IAOI,mBAAA;EhBq4HV;EgB54HM;IAOI,mBAAA;EhBw4HV;EgB/4HM;IAOI,mBAAA;EhB24HV;EgBl5HM;IAOI,mBAAA;EhB84HV;EgBr5HM;IAOI,oBAAA;EhBi5HV;EgBx5HM;IAOI,0BAAA;EhBo5HV;EgB35HM;IAOI,yBAAA;EhBu5HV;EgB95HM;IAOI,uBAAA;EhB05HV;EgBj6HM;IAOI,yBAAA;EhB65HV;EgBp6HM;IAOI,uBAAA;EhBg6HV;EgBv6HM;IAOI,uBAAA;EhBm6HV;EgB16HM;IAOI,0BAAA;IAAA,yBAAA;EhBu6HV;EgB96HM;IAOI,gCAAA;IAAA,+BAAA;EhB26HV;EgBl7HM;IAOI,+BAAA;IAAA,8BAAA;EhB+6HV;EgBt7HM;IAOI,6BAAA;IAAA,4BAAA;EhBm7HV;EgB17HM;IAOI,+BAAA;IAAA,8BAAA;EhBu7HV;EgB97HM;IAOI,6BAAA;IAAA,4BAAA;EhB27HV;EgBl8HM;IAOI,6BAAA;IAAA,4BAAA;EhB+7HV;EgBt8HM;IAOI,wBAAA;IAAA,2BAAA;EhBm8HV;EgB18HM;IAOI,8BAAA;IAAA,iCAAA;EhBu8HV;EgB98HM;IAOI,6BAAA;IAAA,gCAAA;EhB28HV;EgBl9HM;IAOI,2BAAA;IAAA,8BAAA;EhB+8HV;EgBt9HM;IAOI,6BAAA;IAAA,gCAAA;EhBm9HV;EgB19HM;IAOI,2BAAA;IAAA,8BAAA;EhBu9HV;EgB99HM;IAOI,2BAAA;IAAA,8BAAA;EhB29HV;EgBl+HM;IAOI,wBAAA;EhB89HV;EgBr+HM;IAOI,8BAAA;EhBi+HV;EgBx+HM;IAOI,6BAAA;EhBo+HV;EgB3+HM;IAOI,2BAAA;EhBu+HV;EgB9+HM;IAOI,6BAAA;EhB0+HV;EgBj/HM;IAOI,2BAAA;EhB6+HV;EgBp/HM;IAOI,2BAAA;EhBg/HV;EgBv/HM;IAOI,0BAAA;EhBm/HV;EgB1/HM;IAOI,gCAAA;EhBs/HV;EgB7/HM;IAOI,+BAAA;EhBy/HV;EgBhgIM;IAOI,6BAAA;EhB4/HV;EgBngIM;IAOI,+BAAA;EhB+/HV;EgBtgIM;IAOI,6BAAA;EhBkgIV;EgBzgIM;IAOI,6BAAA;EhBqgIV;EgB5gIM;IAOI,2BAAA;EhBwgIV;EgB/gIM;IAOI,iCAAA;EhB2gIV;EgBlhIM;IAOI,gCAAA;EhB8gIV;EgBrhIM;IAOI,8BAAA;EhBihIV;EgBxhIM;IAOI,gCAAA;EhBohIV;EgB3hIM;IAOI,8BAAA;EhBuhIV;EgB9hIM;IAOI,8BAAA;EhB0hIV;EgBjiIM;IAOI,yBAAA;EhB6hIV;EgBpiIM;IAOI,+BAAA;EhBgiIV;EgBviIM;IAOI,8BAAA;EhBmiIV;EgB1iIM;IAOI,4BAAA;EhBsiIV;EgB7iIM;IAOI,8BAAA;EhByiIV;EgBhjIM;IAOI,4BAAA;EhB4iIV;EgBnjIM;IAOI,4BAAA;EhB+iIV;EgBtjIM;IAOI,qBAAA;EhBkjIV;EgBzjIM;IAOI,2BAAA;EhBqjIV;EgB5jIM;IAOI,0BAAA;EhBwjIV;EgB/jIM;IAOI,wBAAA;EhB2jIV;EgBlkIM;IAOI,0BAAA;EhB8jIV;EgBrkIM;IAOI,wBAAA;EhBikIV;EgBxkIM;IAOI,2BAAA;IAAA,0BAAA;EhBqkIV;EgB5kIM;IAOI,iCAAA;IAAA,gCAAA;EhBykIV;EgBhlIM;IAOI,gCAAA;IAAA,+BAAA;EhB6kIV;EgBplIM;IAOI,8BAAA;IAAA,6BAAA;EhBilIV;EgBxlIM;IAOI,gCAAA;IAAA,+BAAA;EhBqlIV;EgB5lIM;IAOI,8BAAA;IAAA,6BAAA;EhBylIV;EgBhmIM;IAOI,yBAAA;IAAA,4BAAA;EhB6lIV;EgBpmIM;IAOI,+BAAA;IAAA,kCAAA;EhBimIV;EgBxmIM;IAOI,8BAAA;IAAA,iCAAA;EhBqmIV;EgB5mIM;IAOI,4BAAA;IAAA,+BAAA;EhBymIV;EgBhnIM;IAOI,8BAAA;IAAA,iCAAA;EhB6mIV;EgBpnIM;IAOI,4BAAA;IAAA,+BAAA;EhBinIV;EgBxnIM;IAOI,yBAAA;EhBonIV;EgB3nIM;IAOI,+BAAA;EhBunIV;EgB9nIM;IAOI,8BAAA;EhB0nIV;EgBjoIM;IAOI,4BAAA;EhB6nIV;EgBpoIM;IAOI,8BAAA;EhBgoIV;EgBvoIM;IAOI,4BAAA;EhBmoIV;EgB1oIM;IAOI,2BAAA;EhBsoIV;EgB7oIM;IAOI,iCAAA;EhByoIV;EgBhpIM;IAOI,gCAAA;EhB4oIV;EgBnpIM;IAOI,8BAAA;EhB+oIV;EgBtpIM;IAOI,gCAAA;EhBkpIV;EgBzpIM;IAOI,8BAAA;EhBqpIV;EgB5pIM;IAOI,4BAAA;EhBwpIV;EgB/pIM;IAOI,kCAAA;EhB2pIV;EgBlqIM;IAOI,iCAAA;EhB8pIV;EgBrqIM;IAOI,+BAAA;EhBiqIV;EgBxqIM;IAOI,iCAAA;EhBoqIV;EgB3qIM;IAOI,+BAAA;EhBuqIV;EgB9qIM;IAOI,0BAAA;EhB0qIV;EgBjrIM;IAOI,gCAAA;EhB6qIV;EgBprIM;IAOI,+BAAA;EhBgrIV;EgBvrIM;IAOI,6BAAA;EhBmrIV;EgB1rIM;IAOI,+BAAA;EhBsrIV;EgB7rIM;IAOI,6BAAA;EhByrIV;EgBhsIM;IAOI,iBAAA;EhB4rIV;EgBnsIM;IAOI,uBAAA;EhB+rIV;EgBtsIM;IAOI,sBAAA;EhBksIV;EgBzsIM;IAOI,oBAAA;EhBqsIV;EgB5sIM;IAOI,sBAAA;EhBwsIV;EgB/sIM;IAOI,oBAAA;EhB2sIV;EgBltIM;IAOI,qBAAA;EhB8sIV;EgBrtIM;IAOI,2BAAA;EhBitIV;EgBxtIM;IAOI,0BAAA;EhBotIV;EgB3tIM;IAOI,wBAAA;EhButIV;EgB9tIM;IAOI,0BAAA;EhB0tIV;EgBjuIM;IAOI,wBAAA;EhB6tIV;EgBpuIM;IAOI,6BAAA;IAAA,wBAAA;EhBguIV;EgBvuIM;IAOI,mCAAA;IAAA,8BAAA;EhBmuIV;EgB1uIM;IAOI,kCAAA;IAAA,6BAAA;EhBsuIV;EgB7uIM;IAOI,gCAAA;IAAA,2BAAA;EhByuIV;EgBhvIM;IAOI,kCAAA;IAAA,6BAAA;EhB4uIV;EgBnvIM;IAOI,gCAAA;IAAA,2BAAA;EhB+uIV;EgBtvIM;IAOI,2BAAA;EhBkvIV;EgBzvIM;IAOI,4BAAA;EhBqvIV;EgB5vIM;IAOI,6BAAA;EhBwvIV;AACF;AQnwII;EQGI;IAOI,sBAAA;EhB6vIV;EgBpwIM;IAOI,uBAAA;EhBgwIV;EgBvwIM;IAOI,sBAAA;EhBmwIV;EgB1wIM;IAOI,iCAAA;IAAA,8BAAA;EhBswIV;EgB7wIM;IAOI,+BAAA;IAAA,4BAAA;EhBywIV;EgBhxIM;IAOI,8BAAA;IAAA,2BAAA;EhB4wIV;EgBnxIM;IAOI,oCAAA;IAAA,iCAAA;EhB+wIV;EgBtxIM;IAOI,8BAAA;IAAA,2BAAA;EhBkxIV;EgBzxIM;IAOI,0BAAA;EhBqxIV;EgB5xIM;IAOI,gCAAA;EhBwxIV;EgB/xIM;IAOI,yBAAA;EhB2xIV;EgBlyIM;IAOI,wBAAA;EhB8xIV;EgBryIM;IAOI,yBAAA;EhBiyIV;EgBxyIM;IAOI,6BAAA;EhBoyIV;EgB3yIM;IAOI,8BAAA;EhBuyIV;EgB9yIM;IAOI,wBAAA;EhB0yIV;EgBjzIM;IAOI,+BAAA;EhB6yIV;EgBpzIM;IAOI,wBAAA;EhBgzIV;EgBvzIM;IAOI,yBAAA;EhBmzIV;EgB1zIM;IAOI,8BAAA;EhBszIV;EgB7zIM;IAOI,iCAAA;EhByzIV;EgBh0IM;IAOI,sCAAA;EhB4zIV;EgBn0IM;IAOI,yCAAA;EhB+zIV;EgBt0IM;IAOI,uBAAA;EhBk0IV;EgBz0IM;IAOI,uBAAA;EhBq0IV;EgB50IM;IAOI,yBAAA;EhBw0IV;EgB/0IM;IAOI,yBAAA;EhB20IV;EgBl1IM;IAOI,0BAAA;EhB80IV;EgBr1IM;IAOI,4BAAA;EhBi1IV;EgBx1IM;IAOI,kCAAA;EhBo1IV;EgB31IM;IAOI,sCAAA;EhBu1IV;EgB91IM;IAOI,oCAAA;EhB01IV;EgBj2IM;IAOI,kCAAA;EhB61IV;EgBp2IM;IAOI,yCAAA;EhBg2IV;EgBv2IM;IAOI,wCAAA;EhBm2IV;EgB12IM;IAOI,wCAAA;EhBs2IV;EgB72IM;IAOI,kCAAA;EhBy2IV;EgBh3IM;IAOI,gCAAA;EhB42IV;EgBn3IM;IAOI,8BAAA;EhB+2IV;EgBt3IM;IAOI,gCAAA;EhBk3IV;EgBz3IM;IAOI,+BAAA;EhBq3IV;EgB53IM;IAOI,oCAAA;EhBw3IV;EgB/3IM;IAOI,kCAAA;EhB23IV;EgBl4IM;IAOI,gCAAA;EhB83IV;EgBr4IM;IAOI,uCAAA;EhBi4IV;EgBx4IM;IAOI,sCAAA;EhBo4IV;EgB34IM;IAOI,iCAAA;EhBu4IV;EgB94IM;IAOI,2BAAA;EhB04IV;EgBj5IM;IAOI,iCAAA;EhB64IV;EgBp5IM;IAOI,+BAAA;EhBg5IV;EgBv5IM;IAOI,6BAAA;EhBm5IV;EgB15IM;IAOI,+BAAA;EhBs5IV;EgB75IM;IAOI,8BAAA;EhBy5IV;EgBh6IM;IAOI,oBAAA;EhB45IV;EgBn6IM;IAOI,mBAAA;EhB+5IV;EgBt6IM;IAOI,mBAAA;EhBk6IV;EgBz6IM;IAOI,mBAAA;EhBq6IV;EgB56IM;IAOI,mBAAA;EhBw6IV;EgB/6IM;IAOI,mBAAA;EhB26IV;EgBl7IM;IAOI,mBAAA;EhB86IV;EgBr7IM;IAOI,mBAAA;EhBi7IV;EgBx7IM;IAOI,oBAAA;EhBo7IV;EgB37IM;IAOI,0BAAA;EhBu7IV;EgB97IM;IAOI,yBAAA;EhB07IV;EgBj8IM;IAOI,uBAAA;EhB67IV;EgBp8IM;IAOI,yBAAA;EhBg8IV;EgBv8IM;IAOI,uBAAA;EhBm8IV;EgB18IM;IAOI,uBAAA;EhBs8IV;EgB78IM;IAOI,0BAAA;IAAA,yBAAA;EhB08IV;EgBj9IM;IAOI,gCAAA;IAAA,+BAAA;EhB88IV;EgBr9IM;IAOI,+BAAA;IAAA,8BAAA;EhBk9IV;EgBz9IM;IAOI,6BAAA;IAAA,4BAAA;EhBs9IV;EgB79IM;IAOI,+BAAA;IAAA,8BAAA;EhB09IV;EgBj+IM;IAOI,6BAAA;IAAA,4BAAA;EhB89IV;EgBr+IM;IAOI,6BAAA;IAAA,4BAAA;EhBk+IV;EgBz+IM;IAOI,wBAAA;IAAA,2BAAA;EhBs+IV;EgB7+IM;IAOI,8BAAA;IAAA,iCAAA;EhB0+IV;EgBj/IM;IAOI,6BAAA;IAAA,gCAAA;EhB8+IV;EgBr/IM;IAOI,2BAAA;IAAA,8BAAA;EhBk/IV;EgBz/IM;IAOI,6BAAA;IAAA,gCAAA;EhBs/IV;EgB7/IM;IAOI,2BAAA;IAAA,8BAAA;EhB0/IV;EgBjgJM;IAOI,2BAAA;IAAA,8BAAA;EhB8/IV;EgBrgJM;IAOI,wBAAA;EhBigJV;EgBxgJM;IAOI,8BAAA;EhBogJV;EgB3gJM;IAOI,6BAAA;EhBugJV;EgB9gJM;IAOI,2BAAA;EhB0gJV;EgBjhJM;IAOI,6BAAA;EhB6gJV;EgBphJM;IAOI,2BAAA;EhBghJV;EgBvhJM;IAOI,2BAAA;EhBmhJV;EgB1hJM;IAOI,0BAAA;EhBshJV;EgB7hJM;IAOI,gCAAA;EhByhJV;EgBhiJM;IAOI,+BAAA;EhB4hJV;EgBniJM;IAOI,6BAAA;EhB+hJV;EgBtiJM;IAOI,+BAAA;EhBkiJV;EgBziJM;IAOI,6BAAA;EhBqiJV;EgB5iJM;IAOI,6BAAA;EhBwiJV;EgB/iJM;IAOI,2BAAA;EhB2iJV;EgBljJM;IAOI,iCAAA;EhB8iJV;EgBrjJM;IAOI,gCAAA;EhBijJV;EgBxjJM;IAOI,8BAAA;EhBojJV;EgB3jJM;IAOI,gCAAA;EhBujJV;EgB9jJM;IAOI,8BAAA;EhB0jJV;EgBjkJM;IAOI,8BAAA;EhB6jJV;EgBpkJM;IAOI,yBAAA;EhBgkJV;EgBvkJM;IAOI,+BAAA;EhBmkJV;EgB1kJM;IAOI,8BAAA;EhBskJV;EgB7kJM;IAOI,4BAAA;EhBykJV;EgBhlJM;IAOI,8BAAA;EhB4kJV;EgBnlJM;IAOI,4BAAA;EhB+kJV;EgBtlJM;IAOI,4BAAA;EhBklJV;EgBzlJM;IAOI,qBAAA;EhBqlJV;EgB5lJM;IAOI,2BAAA;EhBwlJV;EgB/lJM;IAOI,0BAAA;EhB2lJV;EgBlmJM;IAOI,wBAAA;EhB8lJV;EgBrmJM;IAOI,0BAAA;EhBimJV;EgBxmJM;IAOI,wBAAA;EhBomJV;EgB3mJM;IAOI,2BAAA;IAAA,0BAAA;EhBwmJV;EgB/mJM;IAOI,iCAAA;IAAA,gCAAA;EhB4mJV;EgBnnJM;IAOI,gCAAA;IAAA,+BAAA;EhBgnJV;EgBvnJM;IAOI,8BAAA;IAAA,6BAAA;EhBonJV;EgB3nJM;IAOI,gCAAA;IAAA,+BAAA;EhBwnJV;EgB/nJM;IAOI,8BAAA;IAAA,6BAAA;EhB4nJV;EgBnoJM;IAOI,yBAAA;IAAA,4BAAA;EhBgoJV;EgBvoJM;IAOI,+BAAA;IAAA,kCAAA;EhBooJV;EgB3oJM;IAOI,8BAAA;IAAA,iCAAA;EhBwoJV;EgB/oJM;IAOI,4BAAA;IAAA,+BAAA;EhB4oJV;EgBnpJM;IAOI,8BAAA;IAAA,iCAAA;EhBgpJV;EgBvpJM;IAOI,4BAAA;IAAA,+BAAA;EhBopJV;EgB3pJM;IAOI,yBAAA;EhBupJV;EgB9pJM;IAOI,+BAAA;EhB0pJV;EgBjqJM;IAOI,8BAAA;EhB6pJV;EgBpqJM;IAOI,4BAAA;EhBgqJV;EgBvqJM;IAOI,8BAAA;EhBmqJV;EgB1qJM;IAOI,4BAAA;EhBsqJV;EgB7qJM;IAOI,2BAAA;EhByqJV;EgBhrJM;IAOI,iCAAA;EhB4qJV;EgBnrJM;IAOI,gCAAA;EhB+qJV;EgBtrJM;IAOI,8BAAA;EhBkrJV;EgBzrJM;IAOI,gCAAA;EhBqrJV;EgB5rJM;IAOI,8BAAA;EhBwrJV;EgB/rJM;IAOI,4BAAA;EhB2rJV;EgBlsJM;IAOI,kCAAA;EhB8rJV;EgBrsJM;IAOI,iCAAA;EhBisJV;EgBxsJM;IAOI,+BAAA;EhBosJV;EgB3sJM;IAOI,iCAAA;EhBusJV;EgB9sJM;IAOI,+BAAA;EhB0sJV;EgBjtJM;IAOI,0BAAA;EhB6sJV;EgBptJM;IAOI,gCAAA;EhBgtJV;EgBvtJM;IAOI,+BAAA;EhBmtJV;EgB1tJM;IAOI,6BAAA;EhBstJV;EgB7tJM;IAOI,+BAAA;EhBytJV;EgBhuJM;IAOI,6BAAA;EhB4tJV;EgBnuJM;IAOI,iBAAA;EhB+tJV;EgBtuJM;IAOI,uBAAA;EhBkuJV;EgBzuJM;IAOI,sBAAA;EhBquJV;EgB5uJM;IAOI,oBAAA;EhBwuJV;EgB/uJM;IAOI,sBAAA;EhB2uJV;EgBlvJM;IAOI,oBAAA;EhB8uJV;EgBrvJM;IAOI,qBAAA;EhBivJV;EgBxvJM;IAOI,2BAAA;EhBovJV;EgB3vJM;IAOI,0BAAA;EhBuvJV;EgB9vJM;IAOI,wBAAA;EhB0vJV;EgBjwJM;IAOI,0BAAA;EhB6vJV;EgBpwJM;IAOI,wBAAA;EhBgwJV;EgBvwJM;IAOI,6BAAA;IAAA,wBAAA;EhBmwJV;EgB1wJM;IAOI,mCAAA;IAAA,8BAAA;EhBswJV;EgB7wJM;IAOI,kCAAA;IAAA,6BAAA;EhBywJV;EgBhxJM;IAOI,gCAAA;IAAA,2BAAA;EhB4wJV;EgBnxJM;IAOI,kCAAA;IAAA,6BAAA;EhB+wJV;EgBtxJM;IAOI,gCAAA;IAAA,2BAAA;EhBkxJV;EgBzxJM;IAOI,2BAAA;EhBqxJV;EgB5xJM;IAOI,4BAAA;EhBwxJV;EgB/xJM;IAOI,6BAAA;EhB2xJV;AACF;AiBl1JA;ED+CQ;IAOI,4BAAA;EhBgyJV;EgBvyJM;IAOI,0BAAA;EhBmyJV;EgB1yJM;IAOI,6BAAA;EhBsyJV;EgB7yJM;IAOI,4BAAA;EhByyJV;AACF;AiB70JA;ED4BQ;IAOI,0BAAA;EhB8yJV;EgBrzJM;IAOI,gCAAA;EhBizJV;EgBxzJM;IAOI,yBAAA;EhBozJV;EgB3zJM;IAOI,wBAAA;EhBuzJV;EgB9zJM;IAOI,yBAAA;EhB0zJV;EgBj0JM;IAOI,6BAAA;EhB6zJV;EgBp0JM;IAOI,8BAAA;EhBg0JV;EgBv0JM;IAOI,wBAAA;EhBm0JV;EgB10JM;IAOI,+BAAA;EhBs0JV;EgB70JM;IAOI,wBAAA;EhBy0JV;AACF", "file": "bootstrap-utilities.css", "sourcesContent": ["@mixin bsBanner($file) {\n  /*!\n   * Bootstrap #{$file} v5.3.0-alpha1 (https://getbootstrap.com/)\n   * Copyright 2011-2022 The Bootstrap Authors\n   * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n   */\n}\n", ":root,\n[data-bs-theme=\"light\"] {\n  // Note: Custom variable values only support SassScript inside `#{}`.\n\n  // Colors\n  //\n  // Generate palettes for full colors, grays, and theme colors.\n\n  @each $color, $value in $colors {\n    --#{$prefix}#{$color}: #{$value};\n  }\n\n  @each $color, $value in $grays {\n    --#{$prefix}gray-#{$color}: #{$value};\n  }\n\n  @each $color, $value in $theme-colors {\n    --#{$prefix}#{$color}: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-rgb {\n    --#{$prefix}#{$color}-rgb: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-text {\n    --#{$prefix}#{$color}-text: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-bg-subtle {\n    --#{$prefix}#{$color}-bg-subtle: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-border-subtle {\n    --#{$prefix}#{$color}-border-subtle: #{$value};\n  }\n\n  --#{$prefix}white-rgb: #{to-rgb($white)};\n  --#{$prefix}black-rgb: #{to-rgb($black)};\n  --#{$prefix}body-color-rgb: #{to-rgb($body-color)};\n  --#{$prefix}body-bg-rgb: #{to-rgb($body-bg)};\n\n  // Fonts\n\n  // Note: Use `inspect` for lists so that quoted items keep the quotes.\n  // See https://github.com/sass/sass/issues/2383#issuecomment-336349172\n  --#{$prefix}font-sans-serif: #{inspect($font-family-sans-serif)};\n  --#{$prefix}font-monospace: #{inspect($font-family-monospace)};\n  --#{$prefix}gradient: #{$gradient};\n\n  // Root and body\n  // scss-docs-start root-body-variables\n  @if $font-size-root != null {\n    --#{$prefix}root-font-size: #{$font-size-root};\n  }\n  --#{$prefix}body-font-family: #{inspect($font-family-base)};\n  @include rfs($font-size-base, --#{$prefix}body-font-size);\n  --#{$prefix}body-font-weight: #{$font-weight-base};\n  --#{$prefix}body-line-height: #{$line-height-base};\n  --#{$prefix}body-color: #{$body-color};\n\n  --#{$prefix}emphasis-color: #{$body-emphasis-color};\n  --#{$prefix}emphasis-color-rgb: #{to-rgb($body-emphasis-color)};\n\n  --#{$prefix}secondary-color: #{$body-secondary-color};\n  --#{$prefix}secondary-color-rgb: #{to-rgb($body-secondary-color)};\n  --#{$prefix}secondary-bg: #{$body-secondary-bg};\n  --#{$prefix}secondary-bg-rgb: #{to-rgb($body-secondary-bg)};\n\n  --#{$prefix}tertiary-color: #{$body-tertiary-color};\n  --#{$prefix}tertiary-color-rgb: #{to-rgb($body-tertiary-color)};\n  --#{$prefix}tertiary-bg: #{$body-tertiary-bg};\n  --#{$prefix}tertiary-bg-rgb: #{to-rgb($body-tertiary-bg)};\n\n  @if $body-text-align != null {\n    --#{$prefix}body-text-align: #{$body-text-align};\n  }\n  --#{$prefix}body-bg: #{$body-bg};\n  --#{$prefix}body-bg-rgb: #{to-rgb($body-bg)};\n  // scss-docs-end root-body-variables\n\n  @if $headings-color != null {\n    --#{$prefix}heading-color: #{$headings-color};\n  }\n\n  --#{$prefix}link-color: #{$link-color};\n  --#{$prefix}link-color-rgb: #{to-rgb($link-color)};\n  --#{$prefix}link-decoration: #{$link-decoration};\n\n  --#{$prefix}link-hover-color: #{$link-hover-color};\n  --#{$prefix}link-hover-color-rgb: #{to-rgb($link-hover-color)};\n\n  @if $link-hover-decoration != null {\n    --#{$prefix}link-hover-decoration: #{$link-hover-decoration};\n  }\n\n  --#{$prefix}code-color: #{$code-color};\n  --#{$prefix}highlight-bg: #{$mark-bg};\n\n  // scss-docs-start root-border-var\n  --#{$prefix}border-width: #{$border-width};\n  --#{$prefix}border-style: #{$border-style};\n  --#{$prefix}border-color: #{$border-color};\n  --#{$prefix}border-color-translucent: #{$border-color-translucent};\n\n  --#{$prefix}border-radius: #{$border-radius};\n  --#{$prefix}border-radius-sm: #{$border-radius-sm};\n  --#{$prefix}border-radius-lg: #{$border-radius-lg};\n  --#{$prefix}border-radius-xl: #{$border-radius-xl};\n  --#{$prefix}border-radius-2xl: #{$border-radius-2xl};\n  --#{$prefix}border-radius-pill: #{$border-radius-pill};\n  // scss-docs-end root-border-var\n\n  --#{$prefix}box-shadow: #{$box-shadow};\n  --#{$prefix}box-shadow-sm: #{$box-shadow-sm};\n  --#{$prefix}box-shadow-lg: #{$box-shadow-lg};\n  --#{$prefix}box-shadow-inset: #{$box-shadow-inset};\n\n  --#{$prefix}emphasis-color: #{$emphasis-color};\n\n  // scss-docs-start form-control-vars\n  --#{$prefix}form-control-bg: var(--#{$prefix}body-bg);\n  --#{$prefix}form-control-disabled-bg: var(--#{$prefix}secondary-bg);\n  // scss-docs-end form-control-vars\n\n  --#{$prefix}highlight-bg: #{$mark-bg};\n\n  @each $name, $value in $grid-breakpoints {\n    --#{$prefix}breakpoint-#{$name}: #{$value};\n  }\n}\n\n@if $enable-dark-mode {\n  @include color-mode(dark, true) {\n    // scss-docs-start root-dark-mode-vars\n    --#{$prefix}body-color: #{$body-color-dark};\n    --#{$prefix}body-color-rgb: #{to-rgb($body-color-dark)};\n    --#{$prefix}body-bg: #{$body-bg-dark};\n    --#{$prefix}body-bg-rgb: #{to-rgb($body-bg-dark)};\n\n    --#{$prefix}emphasis-color: #{$body-emphasis-color-dark};\n    --#{$prefix}emphasis-color-rgb: #{to-rgb($body-emphasis-color-dark)};\n\n    --#{$prefix}secondary-color: #{$body-secondary-color-dark};\n    --#{$prefix}secondary-color-rgb: #{to-rgb($body-secondary-color-dark)};\n    --#{$prefix}secondary-bg: #{$body-secondary-bg-dark};\n    --#{$prefix}secondary-bg-rgb: #{to-rgb($body-secondary-bg-dark)};\n\n    --#{$prefix}tertiary-color: #{$body-tertiary-color-dark};\n    --#{$prefix}tertiary-color-rgb: #{to-rgb($body-tertiary-color-dark)};\n    --#{$prefix}tertiary-bg: #{$body-tertiary-bg-dark};\n    --#{$prefix}tertiary-bg-rgb: #{to-rgb($body-tertiary-bg-dark)};\n\n    --#{$prefix}emphasis-color: #{$emphasis-color-dark};\n\n    --#{$prefix}primary-text: #{$primary-text-dark};\n    --#{$prefix}secondary-text: #{$secondary-text-dark};\n    --#{$prefix}success-text: #{$success-text-dark};\n    --#{$prefix}info-text: #{$info-text-dark};\n    --#{$prefix}warning-text: #{$warning-text-dark};\n    --#{$prefix}danger-text: #{$danger-text-dark};\n    --#{$prefix}light-text: #{$light-text-dark};\n    --#{$prefix}dark-text: #{$dark-text-dark};\n\n    --#{$prefix}primary-bg-subtle: #{$primary-bg-subtle-dark};\n    --#{$prefix}secondary-bg-subtle: #{$secondary-bg-subtle-dark};\n    --#{$prefix}success-bg-subtle: #{$success-bg-subtle-dark};\n    --#{$prefix}info-bg-subtle: #{$info-bg-subtle-dark};\n    --#{$prefix}warning-bg-subtle: #{$warning-bg-subtle-dark};\n    --#{$prefix}danger-bg-subtle: #{$danger-bg-subtle-dark};\n    --#{$prefix}light-bg-subtle: #{$light-bg-subtle-dark};\n    --#{$prefix}dark-bg-subtle: #{$dark-bg-subtle-dark};\n\n    --#{$prefix}primary-border-subtle: #{$primary-border-subtle-dark};\n    --#{$prefix}secondary-border-subtle: #{$secondary-border-subtle-dark};\n    --#{$prefix}success-border-subtle: #{$success-border-subtle-dark};\n    --#{$prefix}info-border-subtle: #{$info-border-subtle-dark};\n    --#{$prefix}warning-border-subtle: #{$warning-border-subtle-dark};\n    --#{$prefix}danger-border-subtle: #{$danger-border-subtle-dark};\n    --#{$prefix}light-border-subtle: #{$light-border-subtle-dark};\n    --#{$prefix}dark-border-subtle: #{$dark-border-subtle-dark};\n\n    --#{$prefix}heading-color: #{$headings-color-dark};\n\n    --#{$prefix}link-color: #{$link-color-dark};\n    --#{$prefix}link-hover-color: #{$link-hover-color-dark};\n    --#{$prefix}link-color-rgb: #{to-rgb($link-color-dark)};\n    --#{$prefix}link-hover-color-rgb: #{to-rgb($link-hover-color-dark)};\n\n    --#{$prefix}code-color: #{$code-color-dark};\n\n    --#{$prefix}border-color: #{$border-color-dark};\n    --#{$prefix}border-color-translucent: #{$border-color-translucent-dark};\n    // scss-docs-end root-dark-mode-vars\n  }\n}\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated responsive values for font sizes, paddings, margins and much more\n//\n// Licensed under MIT (https://github.com/twbs/rfs/blob/main/LICENSE)\n\n// Configuration\n\n// Base value\n$rfs-base-value: 1.25rem !default;\n$rfs-unit: rem !default;\n\n@if $rfs-unit != rem and $rfs-unit != px {\n  @error \"`#{$rfs-unit}` is not a valid unit for $rfs-unit. Use `px` or `rem`.\";\n}\n\n// Breakpoint at where values start decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n@if $rfs-breakpoint-unit != px and $rfs-breakpoint-unit != em and $rfs-breakpoint-unit != rem {\n  @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n}\n\n// Resize values based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != number or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Mode. Possibilities: \"min-media-query\", \"max-media-query\"\n$rfs-mode: min-media-query !default;\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-rfs to false\n$enable-rfs: true !default;\n\n// Cache $rfs-base-value unit\n$rfs-base-value-unit: unit($rfs-base-value);\n\n@function divide($dividend, $divisor, $precision: 10) {\n  $sign: if($dividend > 0 and $divisor > 0 or $dividend < 0 and $divisor < 0, 1, -1);\n  $dividend: abs($dividend);\n  $divisor: abs($divisor);\n  @if $dividend == 0 {\n    @return 0;\n  }\n  @if $divisor == 0 {\n    @error \"Cannot divide by 0\";\n  }\n  $remainder: $dividend;\n  $result: 0;\n  $factor: 10;\n  @while ($remainder > 0 and $precision >= 0) {\n    $quotient: 0;\n    @while ($remainder >= $divisor) {\n      $remainder: $remainder - $divisor;\n      $quotient: $quotient + 1;\n    }\n    $result: $result * 10 + $quotient;\n    $factor: $factor * .1;\n    $remainder: $remainder * 10;\n    $precision: $precision - 1;\n    @if ($precision < 0 and $remainder >= $divisor * 5) {\n      $result: $result + 1;\n    }\n  }\n  $result: $result * $factor * $sign;\n  $dividend-unit: unit($dividend);\n  $divisor-unit: unit($divisor);\n  $unit-map: (\n    \"px\": 1px,\n    \"rem\": 1rem,\n    \"em\": 1em,\n    \"%\": 1%\n  );\n  @if ($dividend-unit != $divisor-unit and map-has-key($unit-map, $dividend-unit)) {\n    $result: $result * map-get($unit-map, $dividend-unit);\n  }\n  @return $result;\n}\n\n// Remove px-unit from $rfs-base-value for calculations\n@if $rfs-base-value-unit == px {\n  $rfs-base-value: divide($rfs-base-value, $rfs-base-value * 0 + 1);\n}\n@else if $rfs-base-value-unit == rem {\n  $rfs-base-value: divide($rfs-base-value, divide($rfs-base-value * 0 + 1, $rfs-rem-value));\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == px {\n  $rfs-breakpoint: divide($rfs-breakpoint, $rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == rem or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: divide($rfs-breakpoint, divide($rfs-breakpoint * 0 + 1, $rfs-rem-value));\n}\n\n// Calculate the media query value\n$rfs-mq-value: if($rfs-breakpoint-unit == px, #{$rfs-breakpoint}px, #{divide($rfs-breakpoint, $rfs-rem-value)}#{$rfs-breakpoint-unit});\n$rfs-mq-property-width: if($rfs-mode == max-media-query, max-width, min-width);\n$rfs-mq-property-height: if($rfs-mode == max-media-query, max-height, min-height);\n\n// Internal mixin used to determine which media query needs to be used\n@mixin _rfs-media-query {\n  @if $rfs-two-dimensional {\n    @if $rfs-mode == max-media-query {\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}), (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n    @else {\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) and (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n  }\n  @else {\n    @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) {\n      @content;\n    }\n  }\n}\n\n// Internal mixin that adds disable classes to the selector if needed.\n@mixin _rfs-rule {\n  @if $rfs-class == disable and $rfs-mode == max-media-query {\n    // Adding an extra class increases specificity, which prevents the media query to override the property\n    &,\n    .disable-rfs &,\n    &.disable-rfs {\n      @content;\n    }\n  }\n  @else if $rfs-class == enable and $rfs-mode == min-media-query {\n    .enable-rfs &,\n    &.enable-rfs {\n      @content;\n    }\n  }\n  @else {\n    @content;\n  }\n}\n\n// Internal mixin that adds enable classes to the selector if needed.\n@mixin _rfs-media-query-rule {\n\n  @if $rfs-class == enable {\n    @if $rfs-mode == min-media-query {\n      @content;\n    }\n\n    @include _rfs-media-query {\n      .enable-rfs &,\n      &.enable-rfs {\n        @content;\n      }\n    }\n  }\n  @else {\n    @if $rfs-class == disable and $rfs-mode == min-media-query {\n      .disable-rfs &,\n      &.disable-rfs {\n        @content;\n      }\n    }\n    @include _rfs-media-query {\n      @content;\n    }\n  }\n}\n\n// Helper function to get the formatted non-responsive value\n@function rfs-value($values) {\n  // Convert to list\n  $values: if(type-of($values) != list, ($values,), $values);\n\n  $val: '';\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value == 0 {\n      $val: $val + ' 0';\n    }\n    @else {\n      // Cache $value unit\n      $unit: if(type-of($value) == \"number\", unit($value), false);\n\n      @if $unit == px {\n        // Convert to rem if needed\n        $val: $val + ' ' + if($rfs-unit == rem, #{divide($value, $value * 0 + $rfs-rem-value)}rem, $value);\n      }\n      @else if $unit == rem {\n        // Convert to px if needed\n        $val: $val + ' ' + if($rfs-unit == px, #{divide($value, $value * 0 + 1) * $rfs-rem-value}px, $value);\n      }\n      @else {\n        // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n        $val: $val + ' ' + $value;\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// Helper function to get the responsive value calculated by RFS\n@function rfs-fluid-value($values) {\n  // Convert to list\n  $values: if(type-of($values) != list, ($values,), $values);\n\n  $val: '';\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value == 0 {\n      $val: $val + ' 0';\n    }\n\n    @else {\n      // Cache $value unit\n      $unit: if(type-of($value) == \"number\", unit($value), false);\n\n      // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n      @if not $unit or $unit != px and $unit != rem {\n        $val: $val + ' ' + $value;\n      }\n\n      @else {\n        // Remove unit from $value for calculations\n        $value: divide($value, $value * 0 + if($unit == px, 1, divide(1, $rfs-rem-value)));\n\n        // Only add the media query if the value is greater than the minimum value\n        @if abs($value) <= $rfs-base-value or not $enable-rfs {\n          $val: $val + ' ' +  if($rfs-unit == rem, #{divide($value, $rfs-rem-value)}rem, #{$value}px);\n        }\n        @else {\n          // Calculate the minimum value\n          $value-min: $rfs-base-value + divide(abs($value) - $rfs-base-value, $rfs-factor);\n\n          // Calculate difference between $value and the minimum value\n          $value-diff: abs($value) - $value-min;\n\n          // Base value formatting\n          $min-width: if($rfs-unit == rem, #{divide($value-min, $rfs-rem-value)}rem, #{$value-min}px);\n\n          // Use negative value if needed\n          $min-width: if($value < 0, -$min-width, $min-width);\n\n          // Use `vmin` if two-dimensional is enabled\n          $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n          // Calculate the variable width between 0 and $rfs-breakpoint\n          $variable-width: #{divide($value-diff * 100, $rfs-breakpoint)}#{$variable-unit};\n\n          // Return the calculated value\n          $val: $val + ' calc(' + $min-width + if($value < 0, ' - ', ' + ') + $variable-width + ')';\n        }\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// RFS mixin\n@mixin rfs($values, $property: font-size) {\n  @if $values != null {\n    $val: rfs-value($values);\n    $fluidVal: rfs-fluid-value($values);\n\n    // Do not print the media query if responsive & non-responsive values are the same\n    @if $val == $fluidVal {\n      #{$property}: $val;\n    }\n    @else {\n      @include _rfs-rule {\n        #{$property}: if($rfs-mode == max-media-query, $val, $fluidVal);\n\n        // Include safari iframe resize fix if needed\n        min-width: if($rfs-safari-iframe-resize-bug-fix, (0 * 1vw), null);\n      }\n\n      @include _rfs-media-query-rule {\n        #{$property}: if($rfs-mode == max-media-query, $fluidVal, $val);\n      }\n    }\n  }\n}\n\n// Shorthand helper mixins\n@mixin font-size($value) {\n  @include rfs($value);\n}\n\n@mixin padding($value) {\n  @include rfs($value, padding);\n}\n\n@mixin padding-top($value) {\n  @include rfs($value, padding-top);\n}\n\n@mixin padding-right($value) {\n  @include rfs($value, padding-right);\n}\n\n@mixin padding-bottom($value) {\n  @include rfs($value, padding-bottom);\n}\n\n@mixin padding-left($value) {\n  @include rfs($value, padding-left);\n}\n\n@mixin margin($value) {\n  @include rfs($value, margin);\n}\n\n@mixin margin-top($value) {\n  @include rfs($value, margin-top);\n}\n\n@mixin margin-right($value) {\n  @include rfs($value, margin-right);\n}\n\n@mixin margin-bottom($value) {\n  @include rfs($value, margin-bottom);\n}\n\n@mixin margin-left($value) {\n  @include rfs($value, margin-left);\n}\n", "/*!\n * Bootstrap Utilities v5.3.0-alpha1 (https://getbootstrap.com/)\n * Copyright 2011-2022 The Bootstrap Authors\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */\n:root,\n[data-bs-theme=light] {\n  --bs-blue: #0d6efd;\n  --bs-indigo: #6610f2;\n  --bs-purple: #6f42c1;\n  --bs-pink: #d63384;\n  --bs-red: #dc3545;\n  --bs-orange: #fd7e14;\n  --bs-yellow: #ffc107;\n  --bs-green: #198754;\n  --bs-teal: #20c997;\n  --bs-cyan: #0dcaf0;\n  --bs-black: #000;\n  --bs-white: #fff;\n  --bs-gray: #6c757d;\n  --bs-gray-dark: #343a40;\n  --bs-gray-100: #f8f9fa;\n  --bs-gray-200: #e9ecef;\n  --bs-gray-300: #dee2e6;\n  --bs-gray-400: #ced4da;\n  --bs-gray-500: #adb5bd;\n  --bs-gray-600: #6c757d;\n  --bs-gray-700: #495057;\n  --bs-gray-800: #343a40;\n  --bs-gray-900: #212529;\n  --bs-primary: #0d6efd;\n  --bs-secondary: #6c757d;\n  --bs-success: #198754;\n  --bs-info: #0dcaf0;\n  --bs-warning: #ffc107;\n  --bs-danger: #dc3545;\n  --bs-light: #f8f9fa;\n  --bs-dark: #212529;\n  --bs-primary-rgb: 13, 110, 253;\n  --bs-secondary-rgb: 108, 117, 125;\n  --bs-success-rgb: 25, 135, 84;\n  --bs-info-rgb: 13, 202, 240;\n  --bs-warning-rgb: 255, 193, 7;\n  --bs-danger-rgb: 220, 53, 69;\n  --bs-light-rgb: 248, 249, 250;\n  --bs-dark-rgb: 33, 37, 41;\n  --bs-primary-text: #0a58ca;\n  --bs-secondary-text: #6c757d;\n  --bs-success-text: #146c43;\n  --bs-info-text: #087990;\n  --bs-warning-text: #997404;\n  --bs-danger-text: #b02a37;\n  --bs-light-text: #6c757d;\n  --bs-dark-text: #495057;\n  --bs-primary-bg-subtle: #cfe2ff;\n  --bs-secondary-bg-subtle: #f8f9fa;\n  --bs-success-bg-subtle: #d1e7dd;\n  --bs-info-bg-subtle: #cff4fc;\n  --bs-warning-bg-subtle: #fff3cd;\n  --bs-danger-bg-subtle: #f8d7da;\n  --bs-light-bg-subtle: #fcfcfd;\n  --bs-dark-bg-subtle: #ced4da;\n  --bs-primary-border-subtle: #9ec5fe;\n  --bs-secondary-border-subtle: #e9ecef;\n  --bs-success-border-subtle: #a3cfbb;\n  --bs-info-border-subtle: #9eeaf9;\n  --bs-warning-border-subtle: #ffe69c;\n  --bs-danger-border-subtle: #f1aeb5;\n  --bs-light-border-subtle: #e9ecef;\n  --bs-dark-border-subtle: #adb5bd;\n  --bs-white-rgb: 255, 255, 255;\n  --bs-black-rgb: 0, 0, 0;\n  --bs-body-color-rgb: 33, 37, 41;\n  --bs-body-bg-rgb: 255, 255, 255;\n  --bs-font-sans-serif: system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", \"Noto Sans\", \"Liberation Sans\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n  --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));\n  --bs-body-font-family: var(--bs-font-sans-serif);\n  --bs-body-font-size: 1rem;\n  --bs-body-font-weight: 400;\n  --bs-body-line-height: 1.5;\n  --bs-body-color: #212529;\n  --bs-emphasis-color: #000;\n  --bs-emphasis-color-rgb: 0, 0, 0;\n  --bs-secondary-color: rgba(33, 37, 41, 0.75);\n  --bs-secondary-color-rgb: 33, 37, 41;\n  --bs-secondary-bg: #e9ecef;\n  --bs-secondary-bg-rgb: 233, 236, 239;\n  --bs-tertiary-color: rgba(33, 37, 41, 0.5);\n  --bs-tertiary-color-rgb: 33, 37, 41;\n  --bs-tertiary-bg: #f8f9fa;\n  --bs-tertiary-bg-rgb: 248, 249, 250;\n  --bs-body-bg: #fff;\n  --bs-body-bg-rgb: 255, 255, 255;\n  --bs-link-color: #0d6efd;\n  --bs-link-color-rgb: 13, 110, 253;\n  --bs-link-decoration: underline;\n  --bs-link-hover-color: #0a58ca;\n  --bs-link-hover-color-rgb: 10, 88, 202;\n  --bs-code-color: #d63384;\n  --bs-highlight-bg: #fff3cd;\n  --bs-border-width: 1px;\n  --bs-border-style: solid;\n  --bs-border-color: #dee2e6;\n  --bs-border-color-translucent: rgba(0, 0, 0, 0.175);\n  --bs-border-radius: 0.375rem;\n  --bs-border-radius-sm: 0.25rem;\n  --bs-border-radius-lg: 0.5rem;\n  --bs-border-radius-xl: 1rem;\n  --bs-border-radius-2xl: 2rem;\n  --bs-border-radius-pill: 50rem;\n  --bs-box-shadow: 0 0.5rem 1rem rgba(var(--bs-body-color-rgb), 0.15);\n  --bs-box-shadow-sm: 0 0.125rem 0.25rem rgba(var(--bs-body-color-rgb), 0.075);\n  --bs-box-shadow-lg: 0 1rem 3rem rgba(var(--bs-body-color-rgb), 0.175);\n  --bs-box-shadow-inset: inset 0 1px 2px rgba(var(--bs-body-color-rgb), 0.075);\n  --bs-emphasis-color: #000;\n  --bs-form-control-bg: var(--bs-body-bg);\n  --bs-form-control-disabled-bg: var(--bs-secondary-bg);\n  --bs-highlight-bg: #fff3cd;\n  --bs-breakpoint-xs: 0;\n  --bs-breakpoint-sm: 576px;\n  --bs-breakpoint-md: 768px;\n  --bs-breakpoint-lg: 992px;\n  --bs-breakpoint-xl: 1200px;\n  --bs-breakpoint-xxl: 1400px;\n}\n\n[data-bs-theme=dark] {\n  --bs-body-color: #adb5bd;\n  --bs-body-color-rgb: 173, 181, 189;\n  --bs-body-bg: #212529;\n  --bs-body-bg-rgb: 33, 37, 41;\n  --bs-emphasis-color: #f8f9fa;\n  --bs-emphasis-color-rgb: 248, 249, 250;\n  --bs-secondary-color: rgba(173, 181, 189, 0.75);\n  --bs-secondary-color-rgb: 173, 181, 189;\n  --bs-secondary-bg: #343a40;\n  --bs-secondary-bg-rgb: 52, 58, 64;\n  --bs-tertiary-color: rgba(173, 181, 189, 0.5);\n  --bs-tertiary-color-rgb: 173, 181, 189;\n  --bs-tertiary-bg: #2b3035;\n  --bs-tertiary-bg-rgb: 43, 48, 53;\n  --bs-emphasis-color: #fff;\n  --bs-primary-text: #6ea8fe;\n  --bs-secondary-text: #dee2e6;\n  --bs-success-text: #75b798;\n  --bs-info-text: #6edff6;\n  --bs-warning-text: #ffda6a;\n  --bs-danger-text: #ea868f;\n  --bs-light-text: #f8f9fa;\n  --bs-dark-text: #dee2e6;\n  --bs-primary-bg-subtle: #031633;\n  --bs-secondary-bg-subtle: #212529;\n  --bs-success-bg-subtle: #051b11;\n  --bs-info-bg-subtle: #032830;\n  --bs-warning-bg-subtle: #332701;\n  --bs-danger-bg-subtle: #2c0b0e;\n  --bs-light-bg-subtle: #343a40;\n  --bs-dark-bg-subtle: #1a1d20;\n  --bs-primary-border-subtle: #084298;\n  --bs-secondary-border-subtle: #495057;\n  --bs-success-border-subtle: #0f5132;\n  --bs-info-border-subtle: #055160;\n  --bs-warning-border-subtle: #664d03;\n  --bs-danger-border-subtle: #842029;\n  --bs-light-border-subtle: #495057;\n  --bs-dark-border-subtle: #343a40;\n  --bs-heading-color: #fff;\n  --bs-link-color: #6ea8fe;\n  --bs-link-hover-color: #9ec5fe;\n  --bs-link-color-rgb: 110, 168, 254;\n  --bs-link-hover-color-rgb: 158, 197, 254;\n  --bs-code-color: #e685b5;\n  --bs-border-color: #495057;\n  --bs-border-color-translucent: rgba(255, 255, 255, 0.15);\n}\n\n.clearfix::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.text-bg-primary {\n  color: #fff !important;\n  background-color: RGBA(13, 110, 253, var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-secondary {\n  color: #fff !important;\n  background-color: RGBA(108, 117, 125, var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-success {\n  color: #fff !important;\n  background-color: RGBA(25, 135, 84, var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-info {\n  color: #000 !important;\n  background-color: RGBA(13, 202, 240, var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-warning {\n  color: #000 !important;\n  background-color: RGBA(255, 193, 7, var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-danger {\n  color: #fff !important;\n  background-color: RGBA(220, 53, 69, var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-light {\n  color: #000 !important;\n  background-color: RGBA(248, 249, 250, var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-dark {\n  color: #fff !important;\n  background-color: RGBA(33, 37, 41, var(--bs-bg-opacity, 1)) !important;\n}\n\n.link-primary {\n  color: #0d6efd !important;\n}\n.link-primary:hover, .link-primary:focus {\n  color: #0a58ca !important;\n}\n\n.link-secondary {\n  color: #6c757d !important;\n}\n.link-secondary:hover, .link-secondary:focus {\n  color: #565e64 !important;\n}\n\n.link-success {\n  color: #198754 !important;\n}\n.link-success:hover, .link-success:focus {\n  color: #146c43 !important;\n}\n\n.link-info {\n  color: #0dcaf0 !important;\n}\n.link-info:hover, .link-info:focus {\n  color: #3dd5f3 !important;\n}\n\n.link-warning {\n  color: #ffc107 !important;\n}\n.link-warning:hover, .link-warning:focus {\n  color: #ffcd39 !important;\n}\n\n.link-danger {\n  color: #dc3545 !important;\n}\n.link-danger:hover, .link-danger:focus {\n  color: #b02a37 !important;\n}\n\n.link-light {\n  color: #f8f9fa !important;\n}\n.link-light:hover, .link-light:focus {\n  color: #f9fafb !important;\n}\n\n.link-dark {\n  color: #212529 !important;\n}\n.link-dark:hover, .link-dark:focus {\n  color: #1a1e21 !important;\n}\n\n.ratio {\n  position: relative;\n  width: 100%;\n}\n.ratio::before {\n  display: block;\n  padding-top: var(--bs-aspect-ratio);\n  content: \"\";\n}\n.ratio > * {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n\n.ratio-1x1 {\n  --bs-aspect-ratio: 100%;\n}\n\n.ratio-4x3 {\n  --bs-aspect-ratio: 75%;\n}\n\n.ratio-16x9 {\n  --bs-aspect-ratio: 56.25%;\n}\n\n.ratio-21x9 {\n  --bs-aspect-ratio: 42.8571428571%;\n}\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1030;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1030;\n}\n\n.sticky-top {\n  position: sticky;\n  top: 0;\n  z-index: 1020;\n}\n\n.sticky-bottom {\n  position: sticky;\n  bottom: 0;\n  z-index: 1020;\n}\n\n@media (min-width: 576px) {\n  .sticky-sm-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n  .sticky-sm-bottom {\n    position: sticky;\n    bottom: 0;\n    z-index: 1020;\n  }\n}\n@media (min-width: 768px) {\n  .sticky-md-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n  .sticky-md-bottom {\n    position: sticky;\n    bottom: 0;\n    z-index: 1020;\n  }\n}\n@media (min-width: 992px) {\n  .sticky-lg-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n  .sticky-lg-bottom {\n    position: sticky;\n    bottom: 0;\n    z-index: 1020;\n  }\n}\n@media (min-width: 1200px) {\n  .sticky-xl-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n  .sticky-xl-bottom {\n    position: sticky;\n    bottom: 0;\n    z-index: 1020;\n  }\n}\n@media (min-width: 1400px) {\n  .sticky-xxl-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n  .sticky-xxl-bottom {\n    position: sticky;\n    bottom: 0;\n    z-index: 1020;\n  }\n}\n.hstack {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  align-self: stretch;\n}\n\n.vstack {\n  display: flex;\n  flex: 1 1 auto;\n  flex-direction: column;\n  align-self: stretch;\n}\n\n.visually-hidden,\n.visually-hidden-focusable:not(:focus):not(:focus-within) {\n  position: absolute !important;\n  width: 1px !important;\n  height: 1px !important;\n  padding: 0 !important;\n  margin: -1px !important;\n  overflow: hidden !important;\n  clip: rect(0, 0, 0, 0) !important;\n  white-space: nowrap !important;\n  border: 0 !important;\n}\n\n.stretched-link::after {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1;\n  content: \"\";\n}\n\n.text-truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.vr {\n  display: inline-block;\n  align-self: stretch;\n  width: 1px;\n  min-height: 1em;\n  background-color: currentcolor;\n  opacity: 0.25;\n}\n\n.align-baseline {\n  vertical-align: baseline !important;\n}\n\n.align-top {\n  vertical-align: top !important;\n}\n\n.align-middle {\n  vertical-align: middle !important;\n}\n\n.align-bottom {\n  vertical-align: bottom !important;\n}\n\n.align-text-bottom {\n  vertical-align: text-bottom !important;\n}\n\n.align-text-top {\n  vertical-align: text-top !important;\n}\n\n.float-start {\n  float: left !important;\n}\n\n.float-end {\n  float: right !important;\n}\n\n.float-none {\n  float: none !important;\n}\n\n.object-fit-contain {\n  object-fit: contain !important;\n}\n\n.object-fit-cover {\n  object-fit: cover !important;\n}\n\n.object-fit-fill {\n  object-fit: fill !important;\n}\n\n.object-fit-scale {\n  object-fit: scale-down !important;\n}\n\n.object-fit-none {\n  object-fit: none !important;\n}\n\n.opacity-0 {\n  opacity: 0 !important;\n}\n\n.opacity-25 {\n  opacity: 0.25 !important;\n}\n\n.opacity-50 {\n  opacity: 0.5 !important;\n}\n\n.opacity-75 {\n  opacity: 0.75 !important;\n}\n\n.opacity-100 {\n  opacity: 1 !important;\n}\n\n.overflow-auto {\n  overflow: auto !important;\n}\n\n.overflow-hidden {\n  overflow: hidden !important;\n}\n\n.overflow-visible {\n  overflow: visible !important;\n}\n\n.overflow-scroll {\n  overflow: scroll !important;\n}\n\n.overflow-x-auto {\n  overflow-x: auto !important;\n}\n\n.overflow-x-hidden {\n  overflow-x: hidden !important;\n}\n\n.overflow-x-visible {\n  overflow-x: visible !important;\n}\n\n.overflow-x-scroll {\n  overflow-x: scroll !important;\n}\n\n.overflow-y-auto {\n  overflow-y: auto !important;\n}\n\n.overflow-y-hidden {\n  overflow-y: hidden !important;\n}\n\n.overflow-y-visible {\n  overflow-y: visible !important;\n}\n\n.overflow-y-scroll {\n  overflow-y: scroll !important;\n}\n\n.d-inline {\n  display: inline !important;\n}\n\n.d-inline-block {\n  display: inline-block !important;\n}\n\n.d-block {\n  display: block !important;\n}\n\n.d-grid {\n  display: grid !important;\n}\n\n.d-table {\n  display: table !important;\n}\n\n.d-table-row {\n  display: table-row !important;\n}\n\n.d-table-cell {\n  display: table-cell !important;\n}\n\n.d-flex {\n  display: flex !important;\n}\n\n.d-inline-flex {\n  display: inline-flex !important;\n}\n\n.d-none {\n  display: none !important;\n}\n\n.shadow {\n  box-shadow: 0 0.5rem 1rem rgba(var(--bs-body-color-rgb), 0.15) !important;\n}\n\n.shadow-sm {\n  box-shadow: 0 0.125rem 0.25rem rgba(var(--bs-body-color-rgb), 0.075) !important;\n}\n\n.shadow-lg {\n  box-shadow: 0 1rem 3rem rgba(var(--bs-body-color-rgb), 0.175) !important;\n}\n\n.shadow-none {\n  box-shadow: none !important;\n}\n\n.position-static {\n  position: static !important;\n}\n\n.position-relative {\n  position: relative !important;\n}\n\n.position-absolute {\n  position: absolute !important;\n}\n\n.position-fixed {\n  position: fixed !important;\n}\n\n.position-sticky {\n  position: sticky !important;\n}\n\n.top-0 {\n  top: 0 !important;\n}\n\n.top-50 {\n  top: 50% !important;\n}\n\n.top-100 {\n  top: 100% !important;\n}\n\n.bottom-0 {\n  bottom: 0 !important;\n}\n\n.bottom-50 {\n  bottom: 50% !important;\n}\n\n.bottom-100 {\n  bottom: 100% !important;\n}\n\n.start-0 {\n  left: 0 !important;\n}\n\n.start-50 {\n  left: 50% !important;\n}\n\n.start-100 {\n  left: 100% !important;\n}\n\n.end-0 {\n  right: 0 !important;\n}\n\n.end-50 {\n  right: 50% !important;\n}\n\n.end-100 {\n  right: 100% !important;\n}\n\n.translate-middle {\n  transform: translate(-50%, -50%) !important;\n}\n\n.translate-middle-x {\n  transform: translateX(-50%) !important;\n}\n\n.translate-middle-y {\n  transform: translateY(-50%) !important;\n}\n\n.border {\n  border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;\n}\n\n.border-0 {\n  border: 0 !important;\n}\n\n.border-top {\n  border-top: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;\n}\n\n.border-top-0 {\n  border-top: 0 !important;\n}\n\n.border-end {\n  border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;\n}\n\n.border-end-0 {\n  border-right: 0 !important;\n}\n\n.border-bottom {\n  border-bottom: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;\n}\n\n.border-bottom-0 {\n  border-bottom: 0 !important;\n}\n\n.border-start {\n  border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;\n}\n\n.border-start-0 {\n  border-left: 0 !important;\n}\n\n.border-primary {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-primary-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-secondary {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-secondary-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-success {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-success-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-info {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-info-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-warning {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-warning-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-danger {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-danger-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-light {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-light-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-dark {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-dark-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-white {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-white-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-primary-subtle {\n  border-color: var(--bs-primary-border-subtle) !important;\n}\n\n.border-secondary-subtle {\n  border-color: var(--bs-secondary-border-subtle) !important;\n}\n\n.border-success-subtle {\n  border-color: var(--bs-success-border-subtle) !important;\n}\n\n.border-info-subtle {\n  border-color: var(--bs-info-border-subtle) !important;\n}\n\n.border-warning-subtle {\n  border-color: var(--bs-warning-border-subtle) !important;\n}\n\n.border-danger-subtle {\n  border-color: var(--bs-danger-border-subtle) !important;\n}\n\n.border-light-subtle {\n  border-color: var(--bs-light-border-subtle) !important;\n}\n\n.border-dark-subtle {\n  border-color: var(--bs-dark-border-subtle) !important;\n}\n\n.border-1 {\n  --bs-border-width: 1px;\n}\n\n.border-2 {\n  --bs-border-width: 2px;\n}\n\n.border-3 {\n  --bs-border-width: 3px;\n}\n\n.border-4 {\n  --bs-border-width: 4px;\n}\n\n.border-5 {\n  --bs-border-width: 5px;\n}\n\n.border-opacity-10 {\n  --bs-border-opacity: 0.1;\n}\n\n.border-opacity-25 {\n  --bs-border-opacity: 0.25;\n}\n\n.border-opacity-50 {\n  --bs-border-opacity: 0.5;\n}\n\n.border-opacity-75 {\n  --bs-border-opacity: 0.75;\n}\n\n.border-opacity-100 {\n  --bs-border-opacity: 1;\n}\n\n.w-25 {\n  width: 25% !important;\n}\n\n.w-50 {\n  width: 50% !important;\n}\n\n.w-75 {\n  width: 75% !important;\n}\n\n.w-100 {\n  width: 100% !important;\n}\n\n.w-auto {\n  width: auto !important;\n}\n\n.mw-100 {\n  max-width: 100% !important;\n}\n\n.vw-100 {\n  width: 100vw !important;\n}\n\n.min-vw-100 {\n  min-width: 100vw !important;\n}\n\n.h-25 {\n  height: 25% !important;\n}\n\n.h-50 {\n  height: 50% !important;\n}\n\n.h-75 {\n  height: 75% !important;\n}\n\n.h-100 {\n  height: 100% !important;\n}\n\n.h-auto {\n  height: auto !important;\n}\n\n.mh-100 {\n  max-height: 100% !important;\n}\n\n.vh-100 {\n  height: 100vh !important;\n}\n\n.min-vh-100 {\n  min-height: 100vh !important;\n}\n\n.flex-fill {\n  flex: 1 1 auto !important;\n}\n\n.flex-row {\n  flex-direction: row !important;\n}\n\n.flex-column {\n  flex-direction: column !important;\n}\n\n.flex-row-reverse {\n  flex-direction: row-reverse !important;\n}\n\n.flex-column-reverse {\n  flex-direction: column-reverse !important;\n}\n\n.flex-grow-0 {\n  flex-grow: 0 !important;\n}\n\n.flex-grow-1 {\n  flex-grow: 1 !important;\n}\n\n.flex-shrink-0 {\n  flex-shrink: 0 !important;\n}\n\n.flex-shrink-1 {\n  flex-shrink: 1 !important;\n}\n\n.flex-wrap {\n  flex-wrap: wrap !important;\n}\n\n.flex-nowrap {\n  flex-wrap: nowrap !important;\n}\n\n.flex-wrap-reverse {\n  flex-wrap: wrap-reverse !important;\n}\n\n.justify-content-start {\n  justify-content: flex-start !important;\n}\n\n.justify-content-end {\n  justify-content: flex-end !important;\n}\n\n.justify-content-center {\n  justify-content: center !important;\n}\n\n.justify-content-between {\n  justify-content: space-between !important;\n}\n\n.justify-content-around {\n  justify-content: space-around !important;\n}\n\n.justify-content-evenly {\n  justify-content: space-evenly !important;\n}\n\n.align-items-start {\n  align-items: flex-start !important;\n}\n\n.align-items-end {\n  align-items: flex-end !important;\n}\n\n.align-items-center {\n  align-items: center !important;\n}\n\n.align-items-baseline {\n  align-items: baseline !important;\n}\n\n.align-items-stretch {\n  align-items: stretch !important;\n}\n\n.align-content-start {\n  align-content: flex-start !important;\n}\n\n.align-content-end {\n  align-content: flex-end !important;\n}\n\n.align-content-center {\n  align-content: center !important;\n}\n\n.align-content-between {\n  align-content: space-between !important;\n}\n\n.align-content-around {\n  align-content: space-around !important;\n}\n\n.align-content-stretch {\n  align-content: stretch !important;\n}\n\n.align-self-auto {\n  align-self: auto !important;\n}\n\n.align-self-start {\n  align-self: flex-start !important;\n}\n\n.align-self-end {\n  align-self: flex-end !important;\n}\n\n.align-self-center {\n  align-self: center !important;\n}\n\n.align-self-baseline {\n  align-self: baseline !important;\n}\n\n.align-self-stretch {\n  align-self: stretch !important;\n}\n\n.order-first {\n  order: -1 !important;\n}\n\n.order-0 {\n  order: 0 !important;\n}\n\n.order-1 {\n  order: 1 !important;\n}\n\n.order-2 {\n  order: 2 !important;\n}\n\n.order-3 {\n  order: 3 !important;\n}\n\n.order-4 {\n  order: 4 !important;\n}\n\n.order-5 {\n  order: 5 !important;\n}\n\n.order-last {\n  order: 6 !important;\n}\n\n.m-0 {\n  margin: 0 !important;\n}\n\n.m-1 {\n  margin: 0.25rem !important;\n}\n\n.m-2 {\n  margin: 0.5rem !important;\n}\n\n.m-3 {\n  margin: 1rem !important;\n}\n\n.m-4 {\n  margin: 1.5rem !important;\n}\n\n.m-5 {\n  margin: 3rem !important;\n}\n\n.m-auto {\n  margin: auto !important;\n}\n\n.mx-0 {\n  margin-right: 0 !important;\n  margin-left: 0 !important;\n}\n\n.mx-1 {\n  margin-right: 0.25rem !important;\n  margin-left: 0.25rem !important;\n}\n\n.mx-2 {\n  margin-right: 0.5rem !important;\n  margin-left: 0.5rem !important;\n}\n\n.mx-3 {\n  margin-right: 1rem !important;\n  margin-left: 1rem !important;\n}\n\n.mx-4 {\n  margin-right: 1.5rem !important;\n  margin-left: 1.5rem !important;\n}\n\n.mx-5 {\n  margin-right: 3rem !important;\n  margin-left: 3rem !important;\n}\n\n.mx-auto {\n  margin-right: auto !important;\n  margin-left: auto !important;\n}\n\n.my-0 {\n  margin-top: 0 !important;\n  margin-bottom: 0 !important;\n}\n\n.my-1 {\n  margin-top: 0.25rem !important;\n  margin-bottom: 0.25rem !important;\n}\n\n.my-2 {\n  margin-top: 0.5rem !important;\n  margin-bottom: 0.5rem !important;\n}\n\n.my-3 {\n  margin-top: 1rem !important;\n  margin-bottom: 1rem !important;\n}\n\n.my-4 {\n  margin-top: 1.5rem !important;\n  margin-bottom: 1.5rem !important;\n}\n\n.my-5 {\n  margin-top: 3rem !important;\n  margin-bottom: 3rem !important;\n}\n\n.my-auto {\n  margin-top: auto !important;\n  margin-bottom: auto !important;\n}\n\n.mt-0 {\n  margin-top: 0 !important;\n}\n\n.mt-1 {\n  margin-top: 0.25rem !important;\n}\n\n.mt-2 {\n  margin-top: 0.5rem !important;\n}\n\n.mt-3 {\n  margin-top: 1rem !important;\n}\n\n.mt-4 {\n  margin-top: 1.5rem !important;\n}\n\n.mt-5 {\n  margin-top: 3rem !important;\n}\n\n.mt-auto {\n  margin-top: auto !important;\n}\n\n.me-0 {\n  margin-right: 0 !important;\n}\n\n.me-1 {\n  margin-right: 0.25rem !important;\n}\n\n.me-2 {\n  margin-right: 0.5rem !important;\n}\n\n.me-3 {\n  margin-right: 1rem !important;\n}\n\n.me-4 {\n  margin-right: 1.5rem !important;\n}\n\n.me-5 {\n  margin-right: 3rem !important;\n}\n\n.me-auto {\n  margin-right: auto !important;\n}\n\n.mb-0 {\n  margin-bottom: 0 !important;\n}\n\n.mb-1 {\n  margin-bottom: 0.25rem !important;\n}\n\n.mb-2 {\n  margin-bottom: 0.5rem !important;\n}\n\n.mb-3 {\n  margin-bottom: 1rem !important;\n}\n\n.mb-4 {\n  margin-bottom: 1.5rem !important;\n}\n\n.mb-5 {\n  margin-bottom: 3rem !important;\n}\n\n.mb-auto {\n  margin-bottom: auto !important;\n}\n\n.ms-0 {\n  margin-left: 0 !important;\n}\n\n.ms-1 {\n  margin-left: 0.25rem !important;\n}\n\n.ms-2 {\n  margin-left: 0.5rem !important;\n}\n\n.ms-3 {\n  margin-left: 1rem !important;\n}\n\n.ms-4 {\n  margin-left: 1.5rem !important;\n}\n\n.ms-5 {\n  margin-left: 3rem !important;\n}\n\n.ms-auto {\n  margin-left: auto !important;\n}\n\n.p-0 {\n  padding: 0 !important;\n}\n\n.p-1 {\n  padding: 0.25rem !important;\n}\n\n.p-2 {\n  padding: 0.5rem !important;\n}\n\n.p-3 {\n  padding: 1rem !important;\n}\n\n.p-4 {\n  padding: 1.5rem !important;\n}\n\n.p-5 {\n  padding: 3rem !important;\n}\n\n.px-0 {\n  padding-right: 0 !important;\n  padding-left: 0 !important;\n}\n\n.px-1 {\n  padding-right: 0.25rem !important;\n  padding-left: 0.25rem !important;\n}\n\n.px-2 {\n  padding-right: 0.5rem !important;\n  padding-left: 0.5rem !important;\n}\n\n.px-3 {\n  padding-right: 1rem !important;\n  padding-left: 1rem !important;\n}\n\n.px-4 {\n  padding-right: 1.5rem !important;\n  padding-left: 1.5rem !important;\n}\n\n.px-5 {\n  padding-right: 3rem !important;\n  padding-left: 3rem !important;\n}\n\n.py-0 {\n  padding-top: 0 !important;\n  padding-bottom: 0 !important;\n}\n\n.py-1 {\n  padding-top: 0.25rem !important;\n  padding-bottom: 0.25rem !important;\n}\n\n.py-2 {\n  padding-top: 0.5rem !important;\n  padding-bottom: 0.5rem !important;\n}\n\n.py-3 {\n  padding-top: 1rem !important;\n  padding-bottom: 1rem !important;\n}\n\n.py-4 {\n  padding-top: 1.5rem !important;\n  padding-bottom: 1.5rem !important;\n}\n\n.py-5 {\n  padding-top: 3rem !important;\n  padding-bottom: 3rem !important;\n}\n\n.pt-0 {\n  padding-top: 0 !important;\n}\n\n.pt-1 {\n  padding-top: 0.25rem !important;\n}\n\n.pt-2 {\n  padding-top: 0.5rem !important;\n}\n\n.pt-3 {\n  padding-top: 1rem !important;\n}\n\n.pt-4 {\n  padding-top: 1.5rem !important;\n}\n\n.pt-5 {\n  padding-top: 3rem !important;\n}\n\n.pe-0 {\n  padding-right: 0 !important;\n}\n\n.pe-1 {\n  padding-right: 0.25rem !important;\n}\n\n.pe-2 {\n  padding-right: 0.5rem !important;\n}\n\n.pe-3 {\n  padding-right: 1rem !important;\n}\n\n.pe-4 {\n  padding-right: 1.5rem !important;\n}\n\n.pe-5 {\n  padding-right: 3rem !important;\n}\n\n.pb-0 {\n  padding-bottom: 0 !important;\n}\n\n.pb-1 {\n  padding-bottom: 0.25rem !important;\n}\n\n.pb-2 {\n  padding-bottom: 0.5rem !important;\n}\n\n.pb-3 {\n  padding-bottom: 1rem !important;\n}\n\n.pb-4 {\n  padding-bottom: 1.5rem !important;\n}\n\n.pb-5 {\n  padding-bottom: 3rem !important;\n}\n\n.ps-0 {\n  padding-left: 0 !important;\n}\n\n.ps-1 {\n  padding-left: 0.25rem !important;\n}\n\n.ps-2 {\n  padding-left: 0.5rem !important;\n}\n\n.ps-3 {\n  padding-left: 1rem !important;\n}\n\n.ps-4 {\n  padding-left: 1.5rem !important;\n}\n\n.ps-5 {\n  padding-left: 3rem !important;\n}\n\n.gap-0 {\n  gap: 0 !important;\n}\n\n.gap-1 {\n  gap: 0.25rem !important;\n}\n\n.gap-2 {\n  gap: 0.5rem !important;\n}\n\n.gap-3 {\n  gap: 1rem !important;\n}\n\n.gap-4 {\n  gap: 1.5rem !important;\n}\n\n.gap-5 {\n  gap: 3rem !important;\n}\n\n.row-gap-0 {\n  row-gap: 0 !important;\n}\n\n.row-gap-1 {\n  row-gap: 0.25rem !important;\n}\n\n.row-gap-2 {\n  row-gap: 0.5rem !important;\n}\n\n.row-gap-3 {\n  row-gap: 1rem !important;\n}\n\n.row-gap-4 {\n  row-gap: 1.5rem !important;\n}\n\n.row-gap-5 {\n  row-gap: 3rem !important;\n}\n\n.column-gap-0 {\n  column-gap: 0 !important;\n}\n\n.column-gap-1 {\n  column-gap: 0.25rem !important;\n}\n\n.column-gap-2 {\n  column-gap: 0.5rem !important;\n}\n\n.column-gap-3 {\n  column-gap: 1rem !important;\n}\n\n.column-gap-4 {\n  column-gap: 1.5rem !important;\n}\n\n.column-gap-5 {\n  column-gap: 3rem !important;\n}\n\n.font-monospace {\n  font-family: var(--bs-font-monospace) !important;\n}\n\n.fs-1 {\n  font-size: calc(1.375rem + 1.5vw) !important;\n}\n\n.fs-2 {\n  font-size: calc(1.325rem + 0.9vw) !important;\n}\n\n.fs-3 {\n  font-size: calc(1.3rem + 0.6vw) !important;\n}\n\n.fs-4 {\n  font-size: calc(1.275rem + 0.3vw) !important;\n}\n\n.fs-5 {\n  font-size: 1.25rem !important;\n}\n\n.fs-6 {\n  font-size: 1rem !important;\n}\n\n.fst-italic {\n  font-style: italic !important;\n}\n\n.fst-normal {\n  font-style: normal !important;\n}\n\n.fw-lighter {\n  font-weight: lighter !important;\n}\n\n.fw-light {\n  font-weight: 300 !important;\n}\n\n.fw-normal {\n  font-weight: 400 !important;\n}\n\n.fw-medium {\n  font-weight: 500 !important;\n}\n\n.fw-semibold {\n  font-weight: 600 !important;\n}\n\n.fw-bold {\n  font-weight: 700 !important;\n}\n\n.fw-bolder {\n  font-weight: bolder !important;\n}\n\n.lh-1 {\n  line-height: 1 !important;\n}\n\n.lh-sm {\n  line-height: 1.25 !important;\n}\n\n.lh-base {\n  line-height: 1.5 !important;\n}\n\n.lh-lg {\n  line-height: 2 !important;\n}\n\n.text-start {\n  text-align: left !important;\n}\n\n.text-end {\n  text-align: right !important;\n}\n\n.text-center {\n  text-align: center !important;\n}\n\n.text-decoration-none {\n  text-decoration: none !important;\n}\n\n.text-decoration-underline {\n  text-decoration: underline !important;\n}\n\n.text-decoration-line-through {\n  text-decoration: line-through !important;\n}\n\n.text-lowercase {\n  text-transform: lowercase !important;\n}\n\n.text-uppercase {\n  text-transform: uppercase !important;\n}\n\n.text-capitalize {\n  text-transform: capitalize !important;\n}\n\n.text-wrap {\n  white-space: normal !important;\n}\n\n.text-nowrap {\n  white-space: nowrap !important;\n}\n\n/* rtl:begin:remove */\n.text-break {\n  word-wrap: break-word !important;\n  word-break: break-word !important;\n}\n\n/* rtl:end:remove */\n.text-primary {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-primary-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-secondary {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-secondary-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-success {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-success-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-info {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-info-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-warning {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-warning-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-danger {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-light {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-light-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-dark {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-black {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-white {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-white-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-body {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-body-color-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-muted {\n  --bs-text-opacity: 1;\n  color: var(--bs-secondary-color) !important;\n}\n\n.text-black-50 {\n  --bs-text-opacity: 1;\n  color: rgba(0, 0, 0, 0.5) !important;\n}\n\n.text-white-50 {\n  --bs-text-opacity: 1;\n  color: rgba(255, 255, 255, 0.5) !important;\n}\n\n.text-body-secondary {\n  --bs-text-opacity: 1;\n  color: var(--bs-secondary-color) !important;\n}\n\n.text-body-tertiary {\n  --bs-text-opacity: 1;\n  color: var(--bs-tertiary-color) !important;\n}\n\n.text-body-emphasis {\n  --bs-text-opacity: 1;\n  color: var(--bs-emphasis-color) !important;\n}\n\n.text-reset {\n  --bs-text-opacity: 1;\n  color: inherit !important;\n}\n\n.text-opacity-25 {\n  --bs-text-opacity: 0.25;\n}\n\n.text-opacity-50 {\n  --bs-text-opacity: 0.5;\n}\n\n.text-opacity-75 {\n  --bs-text-opacity: 0.75;\n}\n\n.text-opacity-100 {\n  --bs-text-opacity: 1;\n}\n\n.text-primary-emphasis {\n  color: var(--bs-primary-text) !important;\n}\n\n.text-secondary-emphasis {\n  color: var(--bs-secondary-text) !important;\n}\n\n.text-success-emphasis {\n  color: var(--bs-success-text) !important;\n}\n\n.text-info-emphasis {\n  color: var(--bs-info-text) !important;\n}\n\n.text-warning-emphasis {\n  color: var(--bs-warning-text) !important;\n}\n\n.text-danger-emphasis {\n  color: var(--bs-danger-text) !important;\n}\n\n.text-light-emphasis {\n  color: var(--bs-light-text) !important;\n}\n\n.text-dark-emphasis {\n  color: var(--bs-dark-text) !important;\n}\n\n.bg-primary {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-primary-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-secondary {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-secondary-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-success {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-success-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-info {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-info-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-warning {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-warning-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-danger {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-danger-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-light {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-light-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-dark {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-black {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-black-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-white {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-body {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-body-bg-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-transparent {\n  --bs-bg-opacity: 1;\n  background-color: transparent !important;\n}\n\n.bg-body-secondary {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-secondary-bg-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-body-tertiary {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-tertiary-bg-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-body-emphasis {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-emphasis-bg-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-opacity-10 {\n  --bs-bg-opacity: 0.1;\n}\n\n.bg-opacity-25 {\n  --bs-bg-opacity: 0.25;\n}\n\n.bg-opacity-50 {\n  --bs-bg-opacity: 0.5;\n}\n\n.bg-opacity-75 {\n  --bs-bg-opacity: 0.75;\n}\n\n.bg-opacity-100 {\n  --bs-bg-opacity: 1;\n}\n\n.bg-primary-subtle {\n  background-color: var(--bs-primary-bg-subtle) !important;\n}\n\n.bg-secondary-subtle {\n  background-color: var(--bs-secondary-bg-subtle) !important;\n}\n\n.bg-success-subtle {\n  background-color: var(--bs-success-bg-subtle) !important;\n}\n\n.bg-info-subtle {\n  background-color: var(--bs-info-bg-subtle) !important;\n}\n\n.bg-warning-subtle {\n  background-color: var(--bs-warning-bg-subtle) !important;\n}\n\n.bg-danger-subtle {\n  background-color: var(--bs-danger-bg-subtle) !important;\n}\n\n.bg-light-subtle {\n  background-color: var(--bs-light-bg-subtle) !important;\n}\n\n.bg-dark-subtle {\n  background-color: var(--bs-dark-bg-subtle) !important;\n}\n\n.bg-gradient {\n  background-image: var(--bs-gradient) !important;\n}\n\n.user-select-all {\n  user-select: all !important;\n}\n\n.user-select-auto {\n  user-select: auto !important;\n}\n\n.user-select-none {\n  user-select: none !important;\n}\n\n.pe-none {\n  pointer-events: none !important;\n}\n\n.pe-auto {\n  pointer-events: auto !important;\n}\n\n.rounded {\n  border-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-0 {\n  border-radius: 0 !important;\n}\n\n.rounded-1 {\n  border-radius: var(--bs-border-radius-sm) !important;\n}\n\n.rounded-2 {\n  border-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-3 {\n  border-radius: var(--bs-border-radius-lg) !important;\n}\n\n.rounded-4 {\n  border-radius: var(--bs-border-radius-xl) !important;\n}\n\n.rounded-5 {\n  border-radius: var(--bs-border-radius-2xl) !important;\n}\n\n.rounded-circle {\n  border-radius: 50% !important;\n}\n\n.rounded-pill {\n  border-radius: var(--bs-border-radius-pill) !important;\n}\n\n.rounded-top {\n  border-top-left-radius: var(--bs-border-radius) !important;\n  border-top-right-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-top-0 {\n  border-top-left-radius: 0 !important;\n  border-top-right-radius: 0 !important;\n}\n\n.rounded-top-1 {\n  border-top-left-radius: var(--bs-border-radius-sm) !important;\n  border-top-right-radius: var(--bs-border-radius-sm) !important;\n}\n\n.rounded-top-2 {\n  border-top-left-radius: var(--bs-border-radius) !important;\n  border-top-right-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-top-3 {\n  border-top-left-radius: var(--bs-border-radius-lg) !important;\n  border-top-right-radius: var(--bs-border-radius-lg) !important;\n}\n\n.rounded-top-4 {\n  border-top-left-radius: var(--bs-border-radius-xl) !important;\n  border-top-right-radius: var(--bs-border-radius-xl) !important;\n}\n\n.rounded-top-5 {\n  border-top-left-radius: var(--bs-border-radius-2xl) !important;\n  border-top-right-radius: var(--bs-border-radius-2xl) !important;\n}\n\n.rounded-top-circle {\n  border-top-left-radius: 50% !important;\n  border-top-right-radius: 50% !important;\n}\n\n.rounded-top-pill {\n  border-top-left-radius: var(--bs-border-radius-pill) !important;\n  border-top-right-radius: var(--bs-border-radius-pill) !important;\n}\n\n.rounded-end {\n  border-top-right-radius: var(--bs-border-radius) !important;\n  border-bottom-right-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-end-0 {\n  border-top-right-radius: 0 !important;\n  border-bottom-right-radius: 0 !important;\n}\n\n.rounded-end-1 {\n  border-top-right-radius: var(--bs-border-radius-sm) !important;\n  border-bottom-right-radius: var(--bs-border-radius-sm) !important;\n}\n\n.rounded-end-2 {\n  border-top-right-radius: var(--bs-border-radius) !important;\n  border-bottom-right-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-end-3 {\n  border-top-right-radius: var(--bs-border-radius-lg) !important;\n  border-bottom-right-radius: var(--bs-border-radius-lg) !important;\n}\n\n.rounded-end-4 {\n  border-top-right-radius: var(--bs-border-radius-xl) !important;\n  border-bottom-right-radius: var(--bs-border-radius-xl) !important;\n}\n\n.rounded-end-5 {\n  border-top-right-radius: var(--bs-border-radius-2xl) !important;\n  border-bottom-right-radius: var(--bs-border-radius-2xl) !important;\n}\n\n.rounded-end-circle {\n  border-top-right-radius: 50% !important;\n  border-bottom-right-radius: 50% !important;\n}\n\n.rounded-end-pill {\n  border-top-right-radius: var(--bs-border-radius-pill) !important;\n  border-bottom-right-radius: var(--bs-border-radius-pill) !important;\n}\n\n.rounded-bottom {\n  border-bottom-right-radius: var(--bs-border-radius) !important;\n  border-bottom-left-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-bottom-0 {\n  border-bottom-right-radius: 0 !important;\n  border-bottom-left-radius: 0 !important;\n}\n\n.rounded-bottom-1 {\n  border-bottom-right-radius: var(--bs-border-radius-sm) !important;\n  border-bottom-left-radius: var(--bs-border-radius-sm) !important;\n}\n\n.rounded-bottom-2 {\n  border-bottom-right-radius: var(--bs-border-radius) !important;\n  border-bottom-left-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-bottom-3 {\n  border-bottom-right-radius: var(--bs-border-radius-lg) !important;\n  border-bottom-left-radius: var(--bs-border-radius-lg) !important;\n}\n\n.rounded-bottom-4 {\n  border-bottom-right-radius: var(--bs-border-radius-xl) !important;\n  border-bottom-left-radius: var(--bs-border-radius-xl) !important;\n}\n\n.rounded-bottom-5 {\n  border-bottom-right-radius: var(--bs-border-radius-2xl) !important;\n  border-bottom-left-radius: var(--bs-border-radius-2xl) !important;\n}\n\n.rounded-bottom-circle {\n  border-bottom-right-radius: 50% !important;\n  border-bottom-left-radius: 50% !important;\n}\n\n.rounded-bottom-pill {\n  border-bottom-right-radius: var(--bs-border-radius-pill) !important;\n  border-bottom-left-radius: var(--bs-border-radius-pill) !important;\n}\n\n.rounded-start {\n  border-bottom-left-radius: var(--bs-border-radius) !important;\n  border-top-left-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-start-0 {\n  border-bottom-left-radius: 0 !important;\n  border-top-left-radius: 0 !important;\n}\n\n.rounded-start-1 {\n  border-bottom-left-radius: var(--bs-border-radius-sm) !important;\n  border-top-left-radius: var(--bs-border-radius-sm) !important;\n}\n\n.rounded-start-2 {\n  border-bottom-left-radius: var(--bs-border-radius) !important;\n  border-top-left-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-start-3 {\n  border-bottom-left-radius: var(--bs-border-radius-lg) !important;\n  border-top-left-radius: var(--bs-border-radius-lg) !important;\n}\n\n.rounded-start-4 {\n  border-bottom-left-radius: var(--bs-border-radius-xl) !important;\n  border-top-left-radius: var(--bs-border-radius-xl) !important;\n}\n\n.rounded-start-5 {\n  border-bottom-left-radius: var(--bs-border-radius-2xl) !important;\n  border-top-left-radius: var(--bs-border-radius-2xl) !important;\n}\n\n.rounded-start-circle {\n  border-bottom-left-radius: 50% !important;\n  border-top-left-radius: 50% !important;\n}\n\n.rounded-start-pill {\n  border-bottom-left-radius: var(--bs-border-radius-pill) !important;\n  border-top-left-radius: var(--bs-border-radius-pill) !important;\n}\n\n.visible {\n  visibility: visible !important;\n}\n\n.invisible {\n  visibility: hidden !important;\n}\n\n.z-n1 {\n  z-index: -1 !important;\n}\n\n.z-0 {\n  z-index: 0 !important;\n}\n\n.z-1 {\n  z-index: 1 !important;\n}\n\n.z-2 {\n  z-index: 2 !important;\n}\n\n.z-3 {\n  z-index: 3 !important;\n}\n\n@media (min-width: 576px) {\n  .float-sm-start {\n    float: left !important;\n  }\n  .float-sm-end {\n    float: right !important;\n  }\n  .float-sm-none {\n    float: none !important;\n  }\n  .object-fit-sm-contain {\n    object-fit: contain !important;\n  }\n  .object-fit-sm-cover {\n    object-fit: cover !important;\n  }\n  .object-fit-sm-fill {\n    object-fit: fill !important;\n  }\n  .object-fit-sm-scale {\n    object-fit: scale-down !important;\n  }\n  .object-fit-sm-none {\n    object-fit: none !important;\n  }\n  .d-sm-inline {\n    display: inline !important;\n  }\n  .d-sm-inline-block {\n    display: inline-block !important;\n  }\n  .d-sm-block {\n    display: block !important;\n  }\n  .d-sm-grid {\n    display: grid !important;\n  }\n  .d-sm-table {\n    display: table !important;\n  }\n  .d-sm-table-row {\n    display: table-row !important;\n  }\n  .d-sm-table-cell {\n    display: table-cell !important;\n  }\n  .d-sm-flex {\n    display: flex !important;\n  }\n  .d-sm-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-sm-none {\n    display: none !important;\n  }\n  .flex-sm-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-sm-row {\n    flex-direction: row !important;\n  }\n  .flex-sm-column {\n    flex-direction: column !important;\n  }\n  .flex-sm-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-sm-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-sm-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-sm-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-sm-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-sm-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-sm-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-sm-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-sm-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-sm-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-sm-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-sm-center {\n    justify-content: center !important;\n  }\n  .justify-content-sm-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-sm-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-sm-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-sm-start {\n    align-items: flex-start !important;\n  }\n  .align-items-sm-end {\n    align-items: flex-end !important;\n  }\n  .align-items-sm-center {\n    align-items: center !important;\n  }\n  .align-items-sm-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-sm-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-sm-start {\n    align-content: flex-start !important;\n  }\n  .align-content-sm-end {\n    align-content: flex-end !important;\n  }\n  .align-content-sm-center {\n    align-content: center !important;\n  }\n  .align-content-sm-between {\n    align-content: space-between !important;\n  }\n  .align-content-sm-around {\n    align-content: space-around !important;\n  }\n  .align-content-sm-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-sm-auto {\n    align-self: auto !important;\n  }\n  .align-self-sm-start {\n    align-self: flex-start !important;\n  }\n  .align-self-sm-end {\n    align-self: flex-end !important;\n  }\n  .align-self-sm-center {\n    align-self: center !important;\n  }\n  .align-self-sm-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-sm-stretch {\n    align-self: stretch !important;\n  }\n  .order-sm-first {\n    order: -1 !important;\n  }\n  .order-sm-0 {\n    order: 0 !important;\n  }\n  .order-sm-1 {\n    order: 1 !important;\n  }\n  .order-sm-2 {\n    order: 2 !important;\n  }\n  .order-sm-3 {\n    order: 3 !important;\n  }\n  .order-sm-4 {\n    order: 4 !important;\n  }\n  .order-sm-5 {\n    order: 5 !important;\n  }\n  .order-sm-last {\n    order: 6 !important;\n  }\n  .m-sm-0 {\n    margin: 0 !important;\n  }\n  .m-sm-1 {\n    margin: 0.25rem !important;\n  }\n  .m-sm-2 {\n    margin: 0.5rem !important;\n  }\n  .m-sm-3 {\n    margin: 1rem !important;\n  }\n  .m-sm-4 {\n    margin: 1.5rem !important;\n  }\n  .m-sm-5 {\n    margin: 3rem !important;\n  }\n  .m-sm-auto {\n    margin: auto !important;\n  }\n  .mx-sm-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-sm-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .mx-sm-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .mx-sm-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .mx-sm-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .mx-sm-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .mx-sm-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-sm-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-sm-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .my-sm-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .my-sm-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .my-sm-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .my-sm-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .my-sm-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-sm-0 {\n    margin-top: 0 !important;\n  }\n  .mt-sm-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mt-sm-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mt-sm-3 {\n    margin-top: 1rem !important;\n  }\n  .mt-sm-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mt-sm-5 {\n    margin-top: 3rem !important;\n  }\n  .mt-sm-auto {\n    margin-top: auto !important;\n  }\n  .me-sm-0 {\n    margin-right: 0 !important;\n  }\n  .me-sm-1 {\n    margin-right: 0.25rem !important;\n  }\n  .me-sm-2 {\n    margin-right: 0.5rem !important;\n  }\n  .me-sm-3 {\n    margin-right: 1rem !important;\n  }\n  .me-sm-4 {\n    margin-right: 1.5rem !important;\n  }\n  .me-sm-5 {\n    margin-right: 3rem !important;\n  }\n  .me-sm-auto {\n    margin-right: auto !important;\n  }\n  .mb-sm-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-sm-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .mb-sm-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .mb-sm-3 {\n    margin-bottom: 1rem !important;\n  }\n  .mb-sm-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .mb-sm-5 {\n    margin-bottom: 3rem !important;\n  }\n  .mb-sm-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-sm-0 {\n    margin-left: 0 !important;\n  }\n  .ms-sm-1 {\n    margin-left: 0.25rem !important;\n  }\n  .ms-sm-2 {\n    margin-left: 0.5rem !important;\n  }\n  .ms-sm-3 {\n    margin-left: 1rem !important;\n  }\n  .ms-sm-4 {\n    margin-left: 1.5rem !important;\n  }\n  .ms-sm-5 {\n    margin-left: 3rem !important;\n  }\n  .ms-sm-auto {\n    margin-left: auto !important;\n  }\n  .p-sm-0 {\n    padding: 0 !important;\n  }\n  .p-sm-1 {\n    padding: 0.25rem !important;\n  }\n  .p-sm-2 {\n    padding: 0.5rem !important;\n  }\n  .p-sm-3 {\n    padding: 1rem !important;\n  }\n  .p-sm-4 {\n    padding: 1.5rem !important;\n  }\n  .p-sm-5 {\n    padding: 3rem !important;\n  }\n  .px-sm-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-sm-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .px-sm-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .px-sm-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .px-sm-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .px-sm-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-sm-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-sm-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .py-sm-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .py-sm-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .py-sm-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .py-sm-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .pt-sm-0 {\n    padding-top: 0 !important;\n  }\n  .pt-sm-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pt-sm-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pt-sm-3 {\n    padding-top: 1rem !important;\n  }\n  .pt-sm-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pt-sm-5 {\n    padding-top: 3rem !important;\n  }\n  .pe-sm-0 {\n    padding-right: 0 !important;\n  }\n  .pe-sm-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pe-sm-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pe-sm-3 {\n    padding-right: 1rem !important;\n  }\n  .pe-sm-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pe-sm-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-sm-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-sm-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pb-sm-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pb-sm-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pb-sm-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pb-sm-5 {\n    padding-bottom: 3rem !important;\n  }\n  .ps-sm-0 {\n    padding-left: 0 !important;\n  }\n  .ps-sm-1 {\n    padding-left: 0.25rem !important;\n  }\n  .ps-sm-2 {\n    padding-left: 0.5rem !important;\n  }\n  .ps-sm-3 {\n    padding-left: 1rem !important;\n  }\n  .ps-sm-4 {\n    padding-left: 1.5rem !important;\n  }\n  .ps-sm-5 {\n    padding-left: 3rem !important;\n  }\n  .gap-sm-0 {\n    gap: 0 !important;\n  }\n  .gap-sm-1 {\n    gap: 0.25rem !important;\n  }\n  .gap-sm-2 {\n    gap: 0.5rem !important;\n  }\n  .gap-sm-3 {\n    gap: 1rem !important;\n  }\n  .gap-sm-4 {\n    gap: 1.5rem !important;\n  }\n  .gap-sm-5 {\n    gap: 3rem !important;\n  }\n  .row-gap-sm-0 {\n    row-gap: 0 !important;\n  }\n  .row-gap-sm-1 {\n    row-gap: 0.25rem !important;\n  }\n  .row-gap-sm-2 {\n    row-gap: 0.5rem !important;\n  }\n  .row-gap-sm-3 {\n    row-gap: 1rem !important;\n  }\n  .row-gap-sm-4 {\n    row-gap: 1.5rem !important;\n  }\n  .row-gap-sm-5 {\n    row-gap: 3rem !important;\n  }\n  .column-gap-sm-0 {\n    column-gap: 0 !important;\n  }\n  .column-gap-sm-1 {\n    column-gap: 0.25rem !important;\n  }\n  .column-gap-sm-2 {\n    column-gap: 0.5rem !important;\n  }\n  .column-gap-sm-3 {\n    column-gap: 1rem !important;\n  }\n  .column-gap-sm-4 {\n    column-gap: 1.5rem !important;\n  }\n  .column-gap-sm-5 {\n    column-gap: 3rem !important;\n  }\n  .text-sm-start {\n    text-align: left !important;\n  }\n  .text-sm-end {\n    text-align: right !important;\n  }\n  .text-sm-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 768px) {\n  .float-md-start {\n    float: left !important;\n  }\n  .float-md-end {\n    float: right !important;\n  }\n  .float-md-none {\n    float: none !important;\n  }\n  .object-fit-md-contain {\n    object-fit: contain !important;\n  }\n  .object-fit-md-cover {\n    object-fit: cover !important;\n  }\n  .object-fit-md-fill {\n    object-fit: fill !important;\n  }\n  .object-fit-md-scale {\n    object-fit: scale-down !important;\n  }\n  .object-fit-md-none {\n    object-fit: none !important;\n  }\n  .d-md-inline {\n    display: inline !important;\n  }\n  .d-md-inline-block {\n    display: inline-block !important;\n  }\n  .d-md-block {\n    display: block !important;\n  }\n  .d-md-grid {\n    display: grid !important;\n  }\n  .d-md-table {\n    display: table !important;\n  }\n  .d-md-table-row {\n    display: table-row !important;\n  }\n  .d-md-table-cell {\n    display: table-cell !important;\n  }\n  .d-md-flex {\n    display: flex !important;\n  }\n  .d-md-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-md-none {\n    display: none !important;\n  }\n  .flex-md-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-md-row {\n    flex-direction: row !important;\n  }\n  .flex-md-column {\n    flex-direction: column !important;\n  }\n  .flex-md-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-md-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-md-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-md-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-md-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-md-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-md-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-md-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-md-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-md-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-md-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-md-center {\n    justify-content: center !important;\n  }\n  .justify-content-md-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-md-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-md-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-md-start {\n    align-items: flex-start !important;\n  }\n  .align-items-md-end {\n    align-items: flex-end !important;\n  }\n  .align-items-md-center {\n    align-items: center !important;\n  }\n  .align-items-md-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-md-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-md-start {\n    align-content: flex-start !important;\n  }\n  .align-content-md-end {\n    align-content: flex-end !important;\n  }\n  .align-content-md-center {\n    align-content: center !important;\n  }\n  .align-content-md-between {\n    align-content: space-between !important;\n  }\n  .align-content-md-around {\n    align-content: space-around !important;\n  }\n  .align-content-md-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-md-auto {\n    align-self: auto !important;\n  }\n  .align-self-md-start {\n    align-self: flex-start !important;\n  }\n  .align-self-md-end {\n    align-self: flex-end !important;\n  }\n  .align-self-md-center {\n    align-self: center !important;\n  }\n  .align-self-md-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-md-stretch {\n    align-self: stretch !important;\n  }\n  .order-md-first {\n    order: -1 !important;\n  }\n  .order-md-0 {\n    order: 0 !important;\n  }\n  .order-md-1 {\n    order: 1 !important;\n  }\n  .order-md-2 {\n    order: 2 !important;\n  }\n  .order-md-3 {\n    order: 3 !important;\n  }\n  .order-md-4 {\n    order: 4 !important;\n  }\n  .order-md-5 {\n    order: 5 !important;\n  }\n  .order-md-last {\n    order: 6 !important;\n  }\n  .m-md-0 {\n    margin: 0 !important;\n  }\n  .m-md-1 {\n    margin: 0.25rem !important;\n  }\n  .m-md-2 {\n    margin: 0.5rem !important;\n  }\n  .m-md-3 {\n    margin: 1rem !important;\n  }\n  .m-md-4 {\n    margin: 1.5rem !important;\n  }\n  .m-md-5 {\n    margin: 3rem !important;\n  }\n  .m-md-auto {\n    margin: auto !important;\n  }\n  .mx-md-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-md-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .mx-md-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .mx-md-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .mx-md-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .mx-md-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .mx-md-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-md-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-md-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .my-md-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .my-md-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .my-md-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .my-md-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .my-md-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-md-0 {\n    margin-top: 0 !important;\n  }\n  .mt-md-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mt-md-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mt-md-3 {\n    margin-top: 1rem !important;\n  }\n  .mt-md-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mt-md-5 {\n    margin-top: 3rem !important;\n  }\n  .mt-md-auto {\n    margin-top: auto !important;\n  }\n  .me-md-0 {\n    margin-right: 0 !important;\n  }\n  .me-md-1 {\n    margin-right: 0.25rem !important;\n  }\n  .me-md-2 {\n    margin-right: 0.5rem !important;\n  }\n  .me-md-3 {\n    margin-right: 1rem !important;\n  }\n  .me-md-4 {\n    margin-right: 1.5rem !important;\n  }\n  .me-md-5 {\n    margin-right: 3rem !important;\n  }\n  .me-md-auto {\n    margin-right: auto !important;\n  }\n  .mb-md-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-md-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .mb-md-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .mb-md-3 {\n    margin-bottom: 1rem !important;\n  }\n  .mb-md-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .mb-md-5 {\n    margin-bottom: 3rem !important;\n  }\n  .mb-md-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-md-0 {\n    margin-left: 0 !important;\n  }\n  .ms-md-1 {\n    margin-left: 0.25rem !important;\n  }\n  .ms-md-2 {\n    margin-left: 0.5rem !important;\n  }\n  .ms-md-3 {\n    margin-left: 1rem !important;\n  }\n  .ms-md-4 {\n    margin-left: 1.5rem !important;\n  }\n  .ms-md-5 {\n    margin-left: 3rem !important;\n  }\n  .ms-md-auto {\n    margin-left: auto !important;\n  }\n  .p-md-0 {\n    padding: 0 !important;\n  }\n  .p-md-1 {\n    padding: 0.25rem !important;\n  }\n  .p-md-2 {\n    padding: 0.5rem !important;\n  }\n  .p-md-3 {\n    padding: 1rem !important;\n  }\n  .p-md-4 {\n    padding: 1.5rem !important;\n  }\n  .p-md-5 {\n    padding: 3rem !important;\n  }\n  .px-md-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-md-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .px-md-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .px-md-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .px-md-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .px-md-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-md-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-md-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .py-md-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .py-md-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .py-md-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .py-md-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .pt-md-0 {\n    padding-top: 0 !important;\n  }\n  .pt-md-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pt-md-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pt-md-3 {\n    padding-top: 1rem !important;\n  }\n  .pt-md-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pt-md-5 {\n    padding-top: 3rem !important;\n  }\n  .pe-md-0 {\n    padding-right: 0 !important;\n  }\n  .pe-md-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pe-md-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pe-md-3 {\n    padding-right: 1rem !important;\n  }\n  .pe-md-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pe-md-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-md-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-md-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pb-md-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pb-md-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pb-md-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pb-md-5 {\n    padding-bottom: 3rem !important;\n  }\n  .ps-md-0 {\n    padding-left: 0 !important;\n  }\n  .ps-md-1 {\n    padding-left: 0.25rem !important;\n  }\n  .ps-md-2 {\n    padding-left: 0.5rem !important;\n  }\n  .ps-md-3 {\n    padding-left: 1rem !important;\n  }\n  .ps-md-4 {\n    padding-left: 1.5rem !important;\n  }\n  .ps-md-5 {\n    padding-left: 3rem !important;\n  }\n  .gap-md-0 {\n    gap: 0 !important;\n  }\n  .gap-md-1 {\n    gap: 0.25rem !important;\n  }\n  .gap-md-2 {\n    gap: 0.5rem !important;\n  }\n  .gap-md-3 {\n    gap: 1rem !important;\n  }\n  .gap-md-4 {\n    gap: 1.5rem !important;\n  }\n  .gap-md-5 {\n    gap: 3rem !important;\n  }\n  .row-gap-md-0 {\n    row-gap: 0 !important;\n  }\n  .row-gap-md-1 {\n    row-gap: 0.25rem !important;\n  }\n  .row-gap-md-2 {\n    row-gap: 0.5rem !important;\n  }\n  .row-gap-md-3 {\n    row-gap: 1rem !important;\n  }\n  .row-gap-md-4 {\n    row-gap: 1.5rem !important;\n  }\n  .row-gap-md-5 {\n    row-gap: 3rem !important;\n  }\n  .column-gap-md-0 {\n    column-gap: 0 !important;\n  }\n  .column-gap-md-1 {\n    column-gap: 0.25rem !important;\n  }\n  .column-gap-md-2 {\n    column-gap: 0.5rem !important;\n  }\n  .column-gap-md-3 {\n    column-gap: 1rem !important;\n  }\n  .column-gap-md-4 {\n    column-gap: 1.5rem !important;\n  }\n  .column-gap-md-5 {\n    column-gap: 3rem !important;\n  }\n  .text-md-start {\n    text-align: left !important;\n  }\n  .text-md-end {\n    text-align: right !important;\n  }\n  .text-md-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 992px) {\n  .float-lg-start {\n    float: left !important;\n  }\n  .float-lg-end {\n    float: right !important;\n  }\n  .float-lg-none {\n    float: none !important;\n  }\n  .object-fit-lg-contain {\n    object-fit: contain !important;\n  }\n  .object-fit-lg-cover {\n    object-fit: cover !important;\n  }\n  .object-fit-lg-fill {\n    object-fit: fill !important;\n  }\n  .object-fit-lg-scale {\n    object-fit: scale-down !important;\n  }\n  .object-fit-lg-none {\n    object-fit: none !important;\n  }\n  .d-lg-inline {\n    display: inline !important;\n  }\n  .d-lg-inline-block {\n    display: inline-block !important;\n  }\n  .d-lg-block {\n    display: block !important;\n  }\n  .d-lg-grid {\n    display: grid !important;\n  }\n  .d-lg-table {\n    display: table !important;\n  }\n  .d-lg-table-row {\n    display: table-row !important;\n  }\n  .d-lg-table-cell {\n    display: table-cell !important;\n  }\n  .d-lg-flex {\n    display: flex !important;\n  }\n  .d-lg-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-lg-none {\n    display: none !important;\n  }\n  .flex-lg-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-lg-row {\n    flex-direction: row !important;\n  }\n  .flex-lg-column {\n    flex-direction: column !important;\n  }\n  .flex-lg-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-lg-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-lg-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-lg-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-lg-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-lg-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-lg-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-lg-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-lg-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-lg-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-lg-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-lg-center {\n    justify-content: center !important;\n  }\n  .justify-content-lg-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-lg-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-lg-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-lg-start {\n    align-items: flex-start !important;\n  }\n  .align-items-lg-end {\n    align-items: flex-end !important;\n  }\n  .align-items-lg-center {\n    align-items: center !important;\n  }\n  .align-items-lg-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-lg-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-lg-start {\n    align-content: flex-start !important;\n  }\n  .align-content-lg-end {\n    align-content: flex-end !important;\n  }\n  .align-content-lg-center {\n    align-content: center !important;\n  }\n  .align-content-lg-between {\n    align-content: space-between !important;\n  }\n  .align-content-lg-around {\n    align-content: space-around !important;\n  }\n  .align-content-lg-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-lg-auto {\n    align-self: auto !important;\n  }\n  .align-self-lg-start {\n    align-self: flex-start !important;\n  }\n  .align-self-lg-end {\n    align-self: flex-end !important;\n  }\n  .align-self-lg-center {\n    align-self: center !important;\n  }\n  .align-self-lg-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-lg-stretch {\n    align-self: stretch !important;\n  }\n  .order-lg-first {\n    order: -1 !important;\n  }\n  .order-lg-0 {\n    order: 0 !important;\n  }\n  .order-lg-1 {\n    order: 1 !important;\n  }\n  .order-lg-2 {\n    order: 2 !important;\n  }\n  .order-lg-3 {\n    order: 3 !important;\n  }\n  .order-lg-4 {\n    order: 4 !important;\n  }\n  .order-lg-5 {\n    order: 5 !important;\n  }\n  .order-lg-last {\n    order: 6 !important;\n  }\n  .m-lg-0 {\n    margin: 0 !important;\n  }\n  .m-lg-1 {\n    margin: 0.25rem !important;\n  }\n  .m-lg-2 {\n    margin: 0.5rem !important;\n  }\n  .m-lg-3 {\n    margin: 1rem !important;\n  }\n  .m-lg-4 {\n    margin: 1.5rem !important;\n  }\n  .m-lg-5 {\n    margin: 3rem !important;\n  }\n  .m-lg-auto {\n    margin: auto !important;\n  }\n  .mx-lg-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-lg-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .mx-lg-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .mx-lg-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .mx-lg-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .mx-lg-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .mx-lg-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-lg-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-lg-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .my-lg-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .my-lg-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .my-lg-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .my-lg-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .my-lg-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-lg-0 {\n    margin-top: 0 !important;\n  }\n  .mt-lg-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mt-lg-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mt-lg-3 {\n    margin-top: 1rem !important;\n  }\n  .mt-lg-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mt-lg-5 {\n    margin-top: 3rem !important;\n  }\n  .mt-lg-auto {\n    margin-top: auto !important;\n  }\n  .me-lg-0 {\n    margin-right: 0 !important;\n  }\n  .me-lg-1 {\n    margin-right: 0.25rem !important;\n  }\n  .me-lg-2 {\n    margin-right: 0.5rem !important;\n  }\n  .me-lg-3 {\n    margin-right: 1rem !important;\n  }\n  .me-lg-4 {\n    margin-right: 1.5rem !important;\n  }\n  .me-lg-5 {\n    margin-right: 3rem !important;\n  }\n  .me-lg-auto {\n    margin-right: auto !important;\n  }\n  .mb-lg-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-lg-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .mb-lg-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .mb-lg-3 {\n    margin-bottom: 1rem !important;\n  }\n  .mb-lg-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .mb-lg-5 {\n    margin-bottom: 3rem !important;\n  }\n  .mb-lg-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-lg-0 {\n    margin-left: 0 !important;\n  }\n  .ms-lg-1 {\n    margin-left: 0.25rem !important;\n  }\n  .ms-lg-2 {\n    margin-left: 0.5rem !important;\n  }\n  .ms-lg-3 {\n    margin-left: 1rem !important;\n  }\n  .ms-lg-4 {\n    margin-left: 1.5rem !important;\n  }\n  .ms-lg-5 {\n    margin-left: 3rem !important;\n  }\n  .ms-lg-auto {\n    margin-left: auto !important;\n  }\n  .p-lg-0 {\n    padding: 0 !important;\n  }\n  .p-lg-1 {\n    padding: 0.25rem !important;\n  }\n  .p-lg-2 {\n    padding: 0.5rem !important;\n  }\n  .p-lg-3 {\n    padding: 1rem !important;\n  }\n  .p-lg-4 {\n    padding: 1.5rem !important;\n  }\n  .p-lg-5 {\n    padding: 3rem !important;\n  }\n  .px-lg-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-lg-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .px-lg-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .px-lg-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .px-lg-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .px-lg-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-lg-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-lg-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .py-lg-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .py-lg-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .py-lg-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .py-lg-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .pt-lg-0 {\n    padding-top: 0 !important;\n  }\n  .pt-lg-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pt-lg-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pt-lg-3 {\n    padding-top: 1rem !important;\n  }\n  .pt-lg-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pt-lg-5 {\n    padding-top: 3rem !important;\n  }\n  .pe-lg-0 {\n    padding-right: 0 !important;\n  }\n  .pe-lg-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pe-lg-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pe-lg-3 {\n    padding-right: 1rem !important;\n  }\n  .pe-lg-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pe-lg-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-lg-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-lg-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pb-lg-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pb-lg-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pb-lg-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pb-lg-5 {\n    padding-bottom: 3rem !important;\n  }\n  .ps-lg-0 {\n    padding-left: 0 !important;\n  }\n  .ps-lg-1 {\n    padding-left: 0.25rem !important;\n  }\n  .ps-lg-2 {\n    padding-left: 0.5rem !important;\n  }\n  .ps-lg-3 {\n    padding-left: 1rem !important;\n  }\n  .ps-lg-4 {\n    padding-left: 1.5rem !important;\n  }\n  .ps-lg-5 {\n    padding-left: 3rem !important;\n  }\n  .gap-lg-0 {\n    gap: 0 !important;\n  }\n  .gap-lg-1 {\n    gap: 0.25rem !important;\n  }\n  .gap-lg-2 {\n    gap: 0.5rem !important;\n  }\n  .gap-lg-3 {\n    gap: 1rem !important;\n  }\n  .gap-lg-4 {\n    gap: 1.5rem !important;\n  }\n  .gap-lg-5 {\n    gap: 3rem !important;\n  }\n  .row-gap-lg-0 {\n    row-gap: 0 !important;\n  }\n  .row-gap-lg-1 {\n    row-gap: 0.25rem !important;\n  }\n  .row-gap-lg-2 {\n    row-gap: 0.5rem !important;\n  }\n  .row-gap-lg-3 {\n    row-gap: 1rem !important;\n  }\n  .row-gap-lg-4 {\n    row-gap: 1.5rem !important;\n  }\n  .row-gap-lg-5 {\n    row-gap: 3rem !important;\n  }\n  .column-gap-lg-0 {\n    column-gap: 0 !important;\n  }\n  .column-gap-lg-1 {\n    column-gap: 0.25rem !important;\n  }\n  .column-gap-lg-2 {\n    column-gap: 0.5rem !important;\n  }\n  .column-gap-lg-3 {\n    column-gap: 1rem !important;\n  }\n  .column-gap-lg-4 {\n    column-gap: 1.5rem !important;\n  }\n  .column-gap-lg-5 {\n    column-gap: 3rem !important;\n  }\n  .text-lg-start {\n    text-align: left !important;\n  }\n  .text-lg-end {\n    text-align: right !important;\n  }\n  .text-lg-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 1200px) {\n  .float-xl-start {\n    float: left !important;\n  }\n  .float-xl-end {\n    float: right !important;\n  }\n  .float-xl-none {\n    float: none !important;\n  }\n  .object-fit-xl-contain {\n    object-fit: contain !important;\n  }\n  .object-fit-xl-cover {\n    object-fit: cover !important;\n  }\n  .object-fit-xl-fill {\n    object-fit: fill !important;\n  }\n  .object-fit-xl-scale {\n    object-fit: scale-down !important;\n  }\n  .object-fit-xl-none {\n    object-fit: none !important;\n  }\n  .d-xl-inline {\n    display: inline !important;\n  }\n  .d-xl-inline-block {\n    display: inline-block !important;\n  }\n  .d-xl-block {\n    display: block !important;\n  }\n  .d-xl-grid {\n    display: grid !important;\n  }\n  .d-xl-table {\n    display: table !important;\n  }\n  .d-xl-table-row {\n    display: table-row !important;\n  }\n  .d-xl-table-cell {\n    display: table-cell !important;\n  }\n  .d-xl-flex {\n    display: flex !important;\n  }\n  .d-xl-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-xl-none {\n    display: none !important;\n  }\n  .flex-xl-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-xl-row {\n    flex-direction: row !important;\n  }\n  .flex-xl-column {\n    flex-direction: column !important;\n  }\n  .flex-xl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-xl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-xl-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-xl-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-xl-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-xl-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-xl-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-xl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-xl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-xl-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-xl-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-xl-center {\n    justify-content: center !important;\n  }\n  .justify-content-xl-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-xl-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-xl-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-xl-start {\n    align-items: flex-start !important;\n  }\n  .align-items-xl-end {\n    align-items: flex-end !important;\n  }\n  .align-items-xl-center {\n    align-items: center !important;\n  }\n  .align-items-xl-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-xl-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-xl-start {\n    align-content: flex-start !important;\n  }\n  .align-content-xl-end {\n    align-content: flex-end !important;\n  }\n  .align-content-xl-center {\n    align-content: center !important;\n  }\n  .align-content-xl-between {\n    align-content: space-between !important;\n  }\n  .align-content-xl-around {\n    align-content: space-around !important;\n  }\n  .align-content-xl-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-xl-auto {\n    align-self: auto !important;\n  }\n  .align-self-xl-start {\n    align-self: flex-start !important;\n  }\n  .align-self-xl-end {\n    align-self: flex-end !important;\n  }\n  .align-self-xl-center {\n    align-self: center !important;\n  }\n  .align-self-xl-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-xl-stretch {\n    align-self: stretch !important;\n  }\n  .order-xl-first {\n    order: -1 !important;\n  }\n  .order-xl-0 {\n    order: 0 !important;\n  }\n  .order-xl-1 {\n    order: 1 !important;\n  }\n  .order-xl-2 {\n    order: 2 !important;\n  }\n  .order-xl-3 {\n    order: 3 !important;\n  }\n  .order-xl-4 {\n    order: 4 !important;\n  }\n  .order-xl-5 {\n    order: 5 !important;\n  }\n  .order-xl-last {\n    order: 6 !important;\n  }\n  .m-xl-0 {\n    margin: 0 !important;\n  }\n  .m-xl-1 {\n    margin: 0.25rem !important;\n  }\n  .m-xl-2 {\n    margin: 0.5rem !important;\n  }\n  .m-xl-3 {\n    margin: 1rem !important;\n  }\n  .m-xl-4 {\n    margin: 1.5rem !important;\n  }\n  .m-xl-5 {\n    margin: 3rem !important;\n  }\n  .m-xl-auto {\n    margin: auto !important;\n  }\n  .mx-xl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-xl-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .mx-xl-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .mx-xl-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .mx-xl-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .mx-xl-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .mx-xl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-xl-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-xl-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .my-xl-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .my-xl-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .my-xl-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .my-xl-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .my-xl-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-xl-0 {\n    margin-top: 0 !important;\n  }\n  .mt-xl-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mt-xl-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mt-xl-3 {\n    margin-top: 1rem !important;\n  }\n  .mt-xl-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mt-xl-5 {\n    margin-top: 3rem !important;\n  }\n  .mt-xl-auto {\n    margin-top: auto !important;\n  }\n  .me-xl-0 {\n    margin-right: 0 !important;\n  }\n  .me-xl-1 {\n    margin-right: 0.25rem !important;\n  }\n  .me-xl-2 {\n    margin-right: 0.5rem !important;\n  }\n  .me-xl-3 {\n    margin-right: 1rem !important;\n  }\n  .me-xl-4 {\n    margin-right: 1.5rem !important;\n  }\n  .me-xl-5 {\n    margin-right: 3rem !important;\n  }\n  .me-xl-auto {\n    margin-right: auto !important;\n  }\n  .mb-xl-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-xl-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .mb-xl-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .mb-xl-3 {\n    margin-bottom: 1rem !important;\n  }\n  .mb-xl-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .mb-xl-5 {\n    margin-bottom: 3rem !important;\n  }\n  .mb-xl-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-xl-0 {\n    margin-left: 0 !important;\n  }\n  .ms-xl-1 {\n    margin-left: 0.25rem !important;\n  }\n  .ms-xl-2 {\n    margin-left: 0.5rem !important;\n  }\n  .ms-xl-3 {\n    margin-left: 1rem !important;\n  }\n  .ms-xl-4 {\n    margin-left: 1.5rem !important;\n  }\n  .ms-xl-5 {\n    margin-left: 3rem !important;\n  }\n  .ms-xl-auto {\n    margin-left: auto !important;\n  }\n  .p-xl-0 {\n    padding: 0 !important;\n  }\n  .p-xl-1 {\n    padding: 0.25rem !important;\n  }\n  .p-xl-2 {\n    padding: 0.5rem !important;\n  }\n  .p-xl-3 {\n    padding: 1rem !important;\n  }\n  .p-xl-4 {\n    padding: 1.5rem !important;\n  }\n  .p-xl-5 {\n    padding: 3rem !important;\n  }\n  .px-xl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-xl-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .px-xl-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .px-xl-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .px-xl-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .px-xl-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-xl-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-xl-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .py-xl-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .py-xl-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .py-xl-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .py-xl-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .pt-xl-0 {\n    padding-top: 0 !important;\n  }\n  .pt-xl-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pt-xl-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pt-xl-3 {\n    padding-top: 1rem !important;\n  }\n  .pt-xl-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pt-xl-5 {\n    padding-top: 3rem !important;\n  }\n  .pe-xl-0 {\n    padding-right: 0 !important;\n  }\n  .pe-xl-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pe-xl-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pe-xl-3 {\n    padding-right: 1rem !important;\n  }\n  .pe-xl-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pe-xl-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-xl-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-xl-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pb-xl-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pb-xl-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pb-xl-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pb-xl-5 {\n    padding-bottom: 3rem !important;\n  }\n  .ps-xl-0 {\n    padding-left: 0 !important;\n  }\n  .ps-xl-1 {\n    padding-left: 0.25rem !important;\n  }\n  .ps-xl-2 {\n    padding-left: 0.5rem !important;\n  }\n  .ps-xl-3 {\n    padding-left: 1rem !important;\n  }\n  .ps-xl-4 {\n    padding-left: 1.5rem !important;\n  }\n  .ps-xl-5 {\n    padding-left: 3rem !important;\n  }\n  .gap-xl-0 {\n    gap: 0 !important;\n  }\n  .gap-xl-1 {\n    gap: 0.25rem !important;\n  }\n  .gap-xl-2 {\n    gap: 0.5rem !important;\n  }\n  .gap-xl-3 {\n    gap: 1rem !important;\n  }\n  .gap-xl-4 {\n    gap: 1.5rem !important;\n  }\n  .gap-xl-5 {\n    gap: 3rem !important;\n  }\n  .row-gap-xl-0 {\n    row-gap: 0 !important;\n  }\n  .row-gap-xl-1 {\n    row-gap: 0.25rem !important;\n  }\n  .row-gap-xl-2 {\n    row-gap: 0.5rem !important;\n  }\n  .row-gap-xl-3 {\n    row-gap: 1rem !important;\n  }\n  .row-gap-xl-4 {\n    row-gap: 1.5rem !important;\n  }\n  .row-gap-xl-5 {\n    row-gap: 3rem !important;\n  }\n  .column-gap-xl-0 {\n    column-gap: 0 !important;\n  }\n  .column-gap-xl-1 {\n    column-gap: 0.25rem !important;\n  }\n  .column-gap-xl-2 {\n    column-gap: 0.5rem !important;\n  }\n  .column-gap-xl-3 {\n    column-gap: 1rem !important;\n  }\n  .column-gap-xl-4 {\n    column-gap: 1.5rem !important;\n  }\n  .column-gap-xl-5 {\n    column-gap: 3rem !important;\n  }\n  .text-xl-start {\n    text-align: left !important;\n  }\n  .text-xl-end {\n    text-align: right !important;\n  }\n  .text-xl-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 1400px) {\n  .float-xxl-start {\n    float: left !important;\n  }\n  .float-xxl-end {\n    float: right !important;\n  }\n  .float-xxl-none {\n    float: none !important;\n  }\n  .object-fit-xxl-contain {\n    object-fit: contain !important;\n  }\n  .object-fit-xxl-cover {\n    object-fit: cover !important;\n  }\n  .object-fit-xxl-fill {\n    object-fit: fill !important;\n  }\n  .object-fit-xxl-scale {\n    object-fit: scale-down !important;\n  }\n  .object-fit-xxl-none {\n    object-fit: none !important;\n  }\n  .d-xxl-inline {\n    display: inline !important;\n  }\n  .d-xxl-inline-block {\n    display: inline-block !important;\n  }\n  .d-xxl-block {\n    display: block !important;\n  }\n  .d-xxl-grid {\n    display: grid !important;\n  }\n  .d-xxl-table {\n    display: table !important;\n  }\n  .d-xxl-table-row {\n    display: table-row !important;\n  }\n  .d-xxl-table-cell {\n    display: table-cell !important;\n  }\n  .d-xxl-flex {\n    display: flex !important;\n  }\n  .d-xxl-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-xxl-none {\n    display: none !important;\n  }\n  .flex-xxl-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-xxl-row {\n    flex-direction: row !important;\n  }\n  .flex-xxl-column {\n    flex-direction: column !important;\n  }\n  .flex-xxl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-xxl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-xxl-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-xxl-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-xxl-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-xxl-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-xxl-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-xxl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-xxl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-xxl-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-xxl-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-xxl-center {\n    justify-content: center !important;\n  }\n  .justify-content-xxl-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-xxl-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-xxl-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-xxl-start {\n    align-items: flex-start !important;\n  }\n  .align-items-xxl-end {\n    align-items: flex-end !important;\n  }\n  .align-items-xxl-center {\n    align-items: center !important;\n  }\n  .align-items-xxl-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-xxl-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-xxl-start {\n    align-content: flex-start !important;\n  }\n  .align-content-xxl-end {\n    align-content: flex-end !important;\n  }\n  .align-content-xxl-center {\n    align-content: center !important;\n  }\n  .align-content-xxl-between {\n    align-content: space-between !important;\n  }\n  .align-content-xxl-around {\n    align-content: space-around !important;\n  }\n  .align-content-xxl-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-xxl-auto {\n    align-self: auto !important;\n  }\n  .align-self-xxl-start {\n    align-self: flex-start !important;\n  }\n  .align-self-xxl-end {\n    align-self: flex-end !important;\n  }\n  .align-self-xxl-center {\n    align-self: center !important;\n  }\n  .align-self-xxl-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-xxl-stretch {\n    align-self: stretch !important;\n  }\n  .order-xxl-first {\n    order: -1 !important;\n  }\n  .order-xxl-0 {\n    order: 0 !important;\n  }\n  .order-xxl-1 {\n    order: 1 !important;\n  }\n  .order-xxl-2 {\n    order: 2 !important;\n  }\n  .order-xxl-3 {\n    order: 3 !important;\n  }\n  .order-xxl-4 {\n    order: 4 !important;\n  }\n  .order-xxl-5 {\n    order: 5 !important;\n  }\n  .order-xxl-last {\n    order: 6 !important;\n  }\n  .m-xxl-0 {\n    margin: 0 !important;\n  }\n  .m-xxl-1 {\n    margin: 0.25rem !important;\n  }\n  .m-xxl-2 {\n    margin: 0.5rem !important;\n  }\n  .m-xxl-3 {\n    margin: 1rem !important;\n  }\n  .m-xxl-4 {\n    margin: 1.5rem !important;\n  }\n  .m-xxl-5 {\n    margin: 3rem !important;\n  }\n  .m-xxl-auto {\n    margin: auto !important;\n  }\n  .mx-xxl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-xxl-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n  .mx-xxl-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n  .mx-xxl-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n  .mx-xxl-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n  .mx-xxl-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n  .mx-xxl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-xxl-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-xxl-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n  .my-xxl-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n  .my-xxl-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n  .my-xxl-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n  .my-xxl-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n  .my-xxl-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-xxl-0 {\n    margin-top: 0 !important;\n  }\n  .mt-xxl-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mt-xxl-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mt-xxl-3 {\n    margin-top: 1rem !important;\n  }\n  .mt-xxl-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mt-xxl-5 {\n    margin-top: 3rem !important;\n  }\n  .mt-xxl-auto {\n    margin-top: auto !important;\n  }\n  .me-xxl-0 {\n    margin-right: 0 !important;\n  }\n  .me-xxl-1 {\n    margin-right: 0.25rem !important;\n  }\n  .me-xxl-2 {\n    margin-right: 0.5rem !important;\n  }\n  .me-xxl-3 {\n    margin-right: 1rem !important;\n  }\n  .me-xxl-4 {\n    margin-right: 1.5rem !important;\n  }\n  .me-xxl-5 {\n    margin-right: 3rem !important;\n  }\n  .me-xxl-auto {\n    margin-right: auto !important;\n  }\n  .mb-xxl-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-xxl-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .mb-xxl-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .mb-xxl-3 {\n    margin-bottom: 1rem !important;\n  }\n  .mb-xxl-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .mb-xxl-5 {\n    margin-bottom: 3rem !important;\n  }\n  .mb-xxl-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-xxl-0 {\n    margin-left: 0 !important;\n  }\n  .ms-xxl-1 {\n    margin-left: 0.25rem !important;\n  }\n  .ms-xxl-2 {\n    margin-left: 0.5rem !important;\n  }\n  .ms-xxl-3 {\n    margin-left: 1rem !important;\n  }\n  .ms-xxl-4 {\n    margin-left: 1.5rem !important;\n  }\n  .ms-xxl-5 {\n    margin-left: 3rem !important;\n  }\n  .ms-xxl-auto {\n    margin-left: auto !important;\n  }\n  .p-xxl-0 {\n    padding: 0 !important;\n  }\n  .p-xxl-1 {\n    padding: 0.25rem !important;\n  }\n  .p-xxl-2 {\n    padding: 0.5rem !important;\n  }\n  .p-xxl-3 {\n    padding: 1rem !important;\n  }\n  .p-xxl-4 {\n    padding: 1.5rem !important;\n  }\n  .p-xxl-5 {\n    padding: 3rem !important;\n  }\n  .px-xxl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-xxl-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n  .px-xxl-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n  .px-xxl-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n  .px-xxl-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n  .px-xxl-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n  .py-xxl-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-xxl-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n  .py-xxl-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n  .py-xxl-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n  .py-xxl-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n  .py-xxl-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n  .pt-xxl-0 {\n    padding-top: 0 !important;\n  }\n  .pt-xxl-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pt-xxl-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pt-xxl-3 {\n    padding-top: 1rem !important;\n  }\n  .pt-xxl-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pt-xxl-5 {\n    padding-top: 3rem !important;\n  }\n  .pe-xxl-0 {\n    padding-right: 0 !important;\n  }\n  .pe-xxl-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pe-xxl-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pe-xxl-3 {\n    padding-right: 1rem !important;\n  }\n  .pe-xxl-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pe-xxl-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-xxl-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-xxl-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pb-xxl-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pb-xxl-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pb-xxl-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pb-xxl-5 {\n    padding-bottom: 3rem !important;\n  }\n  .ps-xxl-0 {\n    padding-left: 0 !important;\n  }\n  .ps-xxl-1 {\n    padding-left: 0.25rem !important;\n  }\n  .ps-xxl-2 {\n    padding-left: 0.5rem !important;\n  }\n  .ps-xxl-3 {\n    padding-left: 1rem !important;\n  }\n  .ps-xxl-4 {\n    padding-left: 1.5rem !important;\n  }\n  .ps-xxl-5 {\n    padding-left: 3rem !important;\n  }\n  .gap-xxl-0 {\n    gap: 0 !important;\n  }\n  .gap-xxl-1 {\n    gap: 0.25rem !important;\n  }\n  .gap-xxl-2 {\n    gap: 0.5rem !important;\n  }\n  .gap-xxl-3 {\n    gap: 1rem !important;\n  }\n  .gap-xxl-4 {\n    gap: 1.5rem !important;\n  }\n  .gap-xxl-5 {\n    gap: 3rem !important;\n  }\n  .row-gap-xxl-0 {\n    row-gap: 0 !important;\n  }\n  .row-gap-xxl-1 {\n    row-gap: 0.25rem !important;\n  }\n  .row-gap-xxl-2 {\n    row-gap: 0.5rem !important;\n  }\n  .row-gap-xxl-3 {\n    row-gap: 1rem !important;\n  }\n  .row-gap-xxl-4 {\n    row-gap: 1.5rem !important;\n  }\n  .row-gap-xxl-5 {\n    row-gap: 3rem !important;\n  }\n  .column-gap-xxl-0 {\n    column-gap: 0 !important;\n  }\n  .column-gap-xxl-1 {\n    column-gap: 0.25rem !important;\n  }\n  .column-gap-xxl-2 {\n    column-gap: 0.5rem !important;\n  }\n  .column-gap-xxl-3 {\n    column-gap: 1rem !important;\n  }\n  .column-gap-xxl-4 {\n    column-gap: 1.5rem !important;\n  }\n  .column-gap-xxl-5 {\n    column-gap: 3rem !important;\n  }\n  .text-xxl-start {\n    text-align: left !important;\n  }\n  .text-xxl-end {\n    text-align: right !important;\n  }\n  .text-xxl-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 1200px) {\n  .fs-1 {\n    font-size: 2.5rem !important;\n  }\n  .fs-2 {\n    font-size: 2rem !important;\n  }\n  .fs-3 {\n    font-size: 1.75rem !important;\n  }\n  .fs-4 {\n    font-size: 1.5rem !important;\n  }\n}\n@media print {\n  .d-print-inline {\n    display: inline !important;\n  }\n  .d-print-inline-block {\n    display: inline-block !important;\n  }\n  .d-print-block {\n    display: block !important;\n  }\n  .d-print-grid {\n    display: grid !important;\n  }\n  .d-print-table {\n    display: table !important;\n  }\n  .d-print-table-row {\n    display: table-row !important;\n  }\n  .d-print-table-cell {\n    display: table-cell !important;\n  }\n  .d-print-flex {\n    display: flex !important;\n  }\n  .d-print-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-print-none {\n    display: none !important;\n  }\n}\n\n/*# sourceMappingURL=bootstrap-utilities.css.map */\n", "// scss-docs-start color-mode-mixin\n@mixin color-mode($mode: light, $root: false) {\n  @if $color-mode-type == \"media-query\" {\n    @if $root == true {\n      @media (prefers-color-scheme: $mode) {\n        :root {\n          @content;\n        }\n      }\n    } @else {\n      @media (prefers-color-scheme: $mode) {\n        @content;\n      }\n    }\n  } @else {\n    [data-bs-theme=\"#{$mode}\"] {\n      @content;\n    }\n  }\n}\n// scss-docs-end color-mode-mixin\n", "// scss-docs-start clearfix\n@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n// scss-docs-end clearfix\n", "// stylelint-disable function-name-case\n\n// All-caps `RGBA()` function used because of this Sass bug: https://github.com/sass/node-sass/issues/2251\n@each $color, $value in $theme-colors {\n  $color-rgb: to-rgb($value);\n  .text-bg-#{$color} {\n    color: color-contrast($value) if($enable-important-utilities, !important, null);\n    background-color: RGBA($color-rgb, var(--#{$prefix}bg-opacity, 1)) if($enable-important-utilities, !important, null);\n  }\n}\n", "@each $color, $value in $theme-colors {\n  .link-#{$color} {\n    color: $value if($enable-important-utilities, !important, null);\n\n    @if $link-shade-percentage != 0 {\n      &:hover,\n      &:focus {\n        color: if(color-contrast($value) == $color-contrast-light, shade-color($value, $link-shade-percentage), tint-color($value, $link-shade-percentage)) if($enable-important-utilities, !important, null);\n      }\n    }\n  }\n}\n", "// Credit: <PERSON> and <PERSON><PERSON><PERSON> CSS.\n\n.ratio {\n  position: relative;\n  width: 100%;\n\n  &::before {\n    display: block;\n    padding-top: var(--#{$prefix}aspect-ratio);\n    content: \"\";\n  }\n\n  > * {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n  }\n}\n\n@each $key, $ratio in $aspect-ratios {\n  .ratio-#{$key} {\n    --#{$prefix}aspect-ratio: #{$ratio};\n  }\n}\n", "// Shorthand\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n// Responsive sticky top and bottom\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .sticky#{$infix}-top {\n      position: sticky;\n      top: 0;\n      z-index: $zindex-sticky;\n    }\n\n    .sticky#{$infix}-bottom {\n      position: sticky;\n      bottom: 0;\n      z-index: $zindex-sticky;\n    }\n  }\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n// scss-docs-start gray-color-variables\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n// scss-docs-end gray-color-variables\n\n// fusv-disable\n// scss-docs-start gray-colors-map\n$grays: (\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900\n) !default;\n// scss-docs-end gray-colors-map\n// fusv-enable\n\n// scss-docs-start color-variables\n$blue:    #0d6efd !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #d63384 !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #198754 !default;\n$teal:    #20c997 !default;\n$cyan:    #0dcaf0 !default;\n// scss-docs-end color-variables\n\n// scss-docs-start colors-map\n$colors: (\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"black\":      $black,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n) !default;\n// scss-docs-end colors-map\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\n$min-contrast-ratio:   4.5 !default;\n\n// Customize the light and dark text colors for use in our color contrast function.\n$color-contrast-dark:      $black !default;\n$color-contrast-light:     $white !default;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%) !default;\n$blue-200: tint-color($blue, 60%) !default;\n$blue-300: tint-color($blue, 40%) !default;\n$blue-400: tint-color($blue, 20%) !default;\n$blue-500: $blue !default;\n$blue-600: shade-color($blue, 20%) !default;\n$blue-700: shade-color($blue, 40%) !default;\n$blue-800: shade-color($blue, 60%) !default;\n$blue-900: shade-color($blue, 80%) !default;\n\n$indigo-100: tint-color($indigo, 80%) !default;\n$indigo-200: tint-color($indigo, 60%) !default;\n$indigo-300: tint-color($indigo, 40%) !default;\n$indigo-400: tint-color($indigo, 20%) !default;\n$indigo-500: $indigo !default;\n$indigo-600: shade-color($indigo, 20%) !default;\n$indigo-700: shade-color($indigo, 40%) !default;\n$indigo-800: shade-color($indigo, 60%) !default;\n$indigo-900: shade-color($indigo, 80%) !default;\n\n$purple-100: tint-color($purple, 80%) !default;\n$purple-200: tint-color($purple, 60%) !default;\n$purple-300: tint-color($purple, 40%) !default;\n$purple-400: tint-color($purple, 20%) !default;\n$purple-500: $purple !default;\n$purple-600: shade-color($purple, 20%) !default;\n$purple-700: shade-color($purple, 40%) !default;\n$purple-800: shade-color($purple, 60%) !default;\n$purple-900: shade-color($purple, 80%) !default;\n\n$pink-100: tint-color($pink, 80%) !default;\n$pink-200: tint-color($pink, 60%) !default;\n$pink-300: tint-color($pink, 40%) !default;\n$pink-400: tint-color($pink, 20%) !default;\n$pink-500: $pink !default;\n$pink-600: shade-color($pink, 20%) !default;\n$pink-700: shade-color($pink, 40%) !default;\n$pink-800: shade-color($pink, 60%) !default;\n$pink-900: shade-color($pink, 80%) !default;\n\n$red-100: tint-color($red, 80%) !default;\n$red-200: tint-color($red, 60%) !default;\n$red-300: tint-color($red, 40%) !default;\n$red-400: tint-color($red, 20%) !default;\n$red-500: $red !default;\n$red-600: shade-color($red, 20%) !default;\n$red-700: shade-color($red, 40%) !default;\n$red-800: shade-color($red, 60%) !default;\n$red-900: shade-color($red, 80%) !default;\n\n$orange-100: tint-color($orange, 80%) !default;\n$orange-200: tint-color($orange, 60%) !default;\n$orange-300: tint-color($orange, 40%) !default;\n$orange-400: tint-color($orange, 20%) !default;\n$orange-500: $orange !default;\n$orange-600: shade-color($orange, 20%) !default;\n$orange-700: shade-color($orange, 40%) !default;\n$orange-800: shade-color($orange, 60%) !default;\n$orange-900: shade-color($orange, 80%) !default;\n\n$yellow-100: tint-color($yellow, 80%) !default;\n$yellow-200: tint-color($yellow, 60%) !default;\n$yellow-300: tint-color($yellow, 40%) !default;\n$yellow-400: tint-color($yellow, 20%) !default;\n$yellow-500: $yellow !default;\n$yellow-600: shade-color($yellow, 20%) !default;\n$yellow-700: shade-color($yellow, 40%) !default;\n$yellow-800: shade-color($yellow, 60%) !default;\n$yellow-900: shade-color($yellow, 80%) !default;\n\n$green-100: tint-color($green, 80%) !default;\n$green-200: tint-color($green, 60%) !default;\n$green-300: tint-color($green, 40%) !default;\n$green-400: tint-color($green, 20%) !default;\n$green-500: $green !default;\n$green-600: shade-color($green, 20%) !default;\n$green-700: shade-color($green, 40%) !default;\n$green-800: shade-color($green, 60%) !default;\n$green-900: shade-color($green, 80%) !default;\n\n$teal-100: tint-color($teal, 80%) !default;\n$teal-200: tint-color($teal, 60%) !default;\n$teal-300: tint-color($teal, 40%) !default;\n$teal-400: tint-color($teal, 20%) !default;\n$teal-500: $teal !default;\n$teal-600: shade-color($teal, 20%) !default;\n$teal-700: shade-color($teal, 40%) !default;\n$teal-800: shade-color($teal, 60%) !default;\n$teal-900: shade-color($teal, 80%) !default;\n\n$cyan-100: tint-color($cyan, 80%) !default;\n$cyan-200: tint-color($cyan, 60%) !default;\n$cyan-300: tint-color($cyan, 40%) !default;\n$cyan-400: tint-color($cyan, 20%) !default;\n$cyan-500: $cyan !default;\n$cyan-600: shade-color($cyan, 20%) !default;\n$cyan-700: shade-color($cyan, 40%) !default;\n$cyan-800: shade-color($cyan, 60%) !default;\n$cyan-900: shade-color($cyan, 80%) !default;\n\n$blues: (\n  \"blue-100\": $blue-100,\n  \"blue-200\": $blue-200,\n  \"blue-300\": $blue-300,\n  \"blue-400\": $blue-400,\n  \"blue-500\": $blue-500,\n  \"blue-600\": $blue-600,\n  \"blue-700\": $blue-700,\n  \"blue-800\": $blue-800,\n  \"blue-900\": $blue-900\n) !default;\n\n$indigos: (\n  \"indigo-100\": $indigo-100,\n  \"indigo-200\": $indigo-200,\n  \"indigo-300\": $indigo-300,\n  \"indigo-400\": $indigo-400,\n  \"indigo-500\": $indigo-500,\n  \"indigo-600\": $indigo-600,\n  \"indigo-700\": $indigo-700,\n  \"indigo-800\": $indigo-800,\n  \"indigo-900\": $indigo-900\n) !default;\n\n$purples: (\n  \"purple-100\": $purple-100,\n  \"purple-200\": $purple-200,\n  \"purple-300\": $purple-300,\n  \"purple-400\": $purple-400,\n  \"purple-500\": $purple-500,\n  \"purple-600\": $purple-600,\n  \"purple-700\": $purple-700,\n  \"purple-800\": $purple-800,\n  \"purple-900\": $purple-900\n) !default;\n\n$pinks: (\n  \"pink-100\": $pink-100,\n  \"pink-200\": $pink-200,\n  \"pink-300\": $pink-300,\n  \"pink-400\": $pink-400,\n  \"pink-500\": $pink-500,\n  \"pink-600\": $pink-600,\n  \"pink-700\": $pink-700,\n  \"pink-800\": $pink-800,\n  \"pink-900\": $pink-900\n) !default;\n\n$reds: (\n  \"red-100\": $red-100,\n  \"red-200\": $red-200,\n  \"red-300\": $red-300,\n  \"red-400\": $red-400,\n  \"red-500\": $red-500,\n  \"red-600\": $red-600,\n  \"red-700\": $red-700,\n  \"red-800\": $red-800,\n  \"red-900\": $red-900\n) !default;\n\n$oranges: (\n  \"orange-100\": $orange-100,\n  \"orange-200\": $orange-200,\n  \"orange-300\": $orange-300,\n  \"orange-400\": $orange-400,\n  \"orange-500\": $orange-500,\n  \"orange-600\": $orange-600,\n  \"orange-700\": $orange-700,\n  \"orange-800\": $orange-800,\n  \"orange-900\": $orange-900\n) !default;\n\n$yellows: (\n  \"yellow-100\": $yellow-100,\n  \"yellow-200\": $yellow-200,\n  \"yellow-300\": $yellow-300,\n  \"yellow-400\": $yellow-400,\n  \"yellow-500\": $yellow-500,\n  \"yellow-600\": $yellow-600,\n  \"yellow-700\": $yellow-700,\n  \"yellow-800\": $yellow-800,\n  \"yellow-900\": $yellow-900\n) !default;\n\n$greens: (\n  \"green-100\": $green-100,\n  \"green-200\": $green-200,\n  \"green-300\": $green-300,\n  \"green-400\": $green-400,\n  \"green-500\": $green-500,\n  \"green-600\": $green-600,\n  \"green-700\": $green-700,\n  \"green-800\": $green-800,\n  \"green-900\": $green-900\n) !default;\n\n$teals: (\n  \"teal-100\": $teal-100,\n  \"teal-200\": $teal-200,\n  \"teal-300\": $teal-300,\n  \"teal-400\": $teal-400,\n  \"teal-500\": $teal-500,\n  \"teal-600\": $teal-600,\n  \"teal-700\": $teal-700,\n  \"teal-800\": $teal-800,\n  \"teal-900\": $teal-900\n) !default;\n\n$cyans: (\n  \"cyan-100\": $cyan-100,\n  \"cyan-200\": $cyan-200,\n  \"cyan-300\": $cyan-300,\n  \"cyan-400\": $cyan-400,\n  \"cyan-500\": $cyan-500,\n  \"cyan-600\": $cyan-600,\n  \"cyan-700\": $cyan-700,\n  \"cyan-800\": $cyan-800,\n  \"cyan-900\": $cyan-900\n) !default;\n// fusv-enable\n\n// scss-docs-start theme-color-variables\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-900 !default;\n// scss-docs-end theme-color-variables\n\n// scss-docs-start theme-colors-map\n$theme-colors: (\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"light\":      $light,\n  \"dark\":       $dark\n) !default;\n// scss-docs-end theme-colors-map\n\n$primary-text:            $blue-600 !default;\n$secondary-text:          $gray-600 !default;\n$success-text:            $green-600 !default;\n$info-text:               $cyan-700 !default;\n$warning-text:            $yellow-700 !default;\n$danger-text:             $red-600 !default;\n$light-text:              $gray-600 !default;\n$dark-text:               $gray-700 !default;\n\n$primary-bg-subtle:       $blue-100 !default;\n$secondary-bg-subtle:     $gray-100 !default;\n$success-bg-subtle:       $green-100 !default;\n$info-bg-subtle:          $cyan-100 !default;\n$warning-bg-subtle:       $yellow-100 !default;\n$danger-bg-subtle:        $red-100 !default;\n$light-bg-subtle:         mix($gray-100, $white) !default;\n$dark-bg-subtle:          $gray-400 !default;\n\n$primary-border-subtle:   $blue-200 !default;\n$secondary-border-subtle: $gray-200 !default;\n$success-border-subtle:   $green-200 !default;\n$info-border-subtle:      $cyan-200 !default;\n$warning-border-subtle:   $yellow-200 !default;\n$danger-border-subtle:    $red-200 !default;\n$light-border-subtle:     $gray-200 !default;\n$dark-border-subtle:      $gray-500 !default;\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                true !default;\n$enable-rounded:              true !default;\n$enable-shadows:              false !default;\n$enable-gradients:            false !default;\n$enable-transitions:          true !default;\n$enable-reduced-motion:       true !default;\n$enable-smooth-scroll:        true !default;\n$enable-grid-classes:         true !default;\n$enable-container-classes:    true !default;\n$enable-cssgrid:              false !default;\n$enable-button-pointers:      true !default;\n$enable-rfs:                  true !default;\n$enable-validation-icons:     true !default;\n$enable-negative-margins:     false !default;\n$enable-deprecation-messages: true !default;\n$enable-important-utilities:  true !default;\n\n$enable-dark-mode:            true !default;\n$color-mode-type:             data !default; // `data` or `media-query`\n\n// Prefix for :root CSS variables\n\n$variable-prefix:             bs- !default; // Deprecated in v5.2.0 for the shorter `$prefix`\n$prefix:                      $variable-prefix !default;\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n// scss-docs-start variable-gradient\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\n// scss-docs-end variable-gradient\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// scss-docs-start spacer-variables-maps\n$spacer: 1rem !default;\n$spacers: (\n  0: 0,\n  1: $spacer * .25,\n  2: $spacer * .5,\n  3: $spacer,\n  4: $spacer * 1.5,\n  5: $spacer * 3,\n) !default;\n// scss-docs-end spacer-variables-maps\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n// scss-docs-start position-map\n$position-values: (\n  0: 0,\n  50: 50%,\n  100: 100%\n) !default;\n// scss-docs-end position-map\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-text-align:           null !default;\n$body-color:                $gray-900 !default;\n$body-bg:                   $white !default;\n\n$body-emphasis-color:       $black !default;\n\n$body-secondary-color:      rgba($body-color, .75) !default;\n$body-secondary-bg:         $gray-200 !default;\n\n$body-tertiary-color:       rgba($body-color, .5) !default;\n$body-tertiary-bg:          $gray-100 !default;\n\n$emphasis-color:            $black !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              $primary !default;\n$link-decoration:                         underline !default;\n$link-shade-percentage:                   20% !default;\n$link-hover-color:                        shift-color($link-color, $link-shade-percentage) !default;\n$link-hover-decoration:                   null !default;\n\n$stretched-link-pseudo-element:           after !default;\n$stretched-link-z-index:                  1 !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px,\n  xxl: 1400px\n) !default;\n// scss-docs-end grid-breakpoints\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px,\n  xxl: 1320px\n) !default;\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           1.5rem !default;\n$grid-row-columns:            6 !default;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// scss-docs-start border-variables\n$border-width:                1px !default;\n$border-widths: (\n  1: 1px,\n  2: 2px,\n  3: 3px,\n  4: 4px,\n  5: 5px\n) !default;\n$border-style:                solid !default;\n$border-color:                $gray-300 !default;\n$border-color-translucent:    rgba($black, .175) !default;\n// scss-docs-end border-variables\n\n// scss-docs-start border-radius-variables\n$border-radius:               .375rem !default;\n$border-radius-sm:            .25rem !default;\n$border-radius-lg:            .5rem !default;\n$border-radius-xl:            1rem !default;\n$border-radius-2xl:           2rem !default;\n$border-radius-pill:          50rem !default;\n// scss-docs-end border-radius-variables\n\n// scss-docs-start box-shadow-variables\n$box-shadow:                  0 .5rem 1rem rgba(var(--#{$prefix}body-color-rgb), .15) !default;\n$box-shadow-sm:               0 .125rem .25rem rgba(var(--#{$prefix}body-color-rgb), .075) !default;\n$box-shadow-lg:               0 1rem 3rem rgba(var(--#{$prefix}body-color-rgb), .175) !default;\n$box-shadow-inset:            inset 0 1px 2px rgba(var(--#{$prefix}body-color-rgb), .075) !default;\n// scss-docs-end box-shadow-variables\n\n$component-active-color:      $white !default;\n$component-active-bg:         $primary !default;\n\n// scss-docs-start caret-variables\n$caret-width:                 .3em !default;\n$caret-vertical-align:        $caret-width * .85 !default;\n$caret-spacing:               $caret-width * .85 !default;\n// scss-docs-end caret-variables\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n// scss-docs-start collapse-transition\n$transition-collapse:         height .35s ease !default;\n$transition-collapse-width:   width .35s ease !default;\n// scss-docs-end collapse-transition\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\n  \"1x1\": 100%,\n  \"4x3\": calc(3 / 4 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 / 21 * 100%)\n) !default;\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// scss-docs-start font-variables\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", \"Noto Sans\", \"Liberation Sans\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n// stylelint-enable value-keyword-case\n$font-family-base:            var(--#{$prefix}font-sans-serif) !default;\n$font-family-code:            var(--#{$prefix}font-monospace) !default;\n\n// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings, and margins\n// $font-size-base affects the font size of the body text\n$font-size-root:              null !default;\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-sm:                $font-size-base * .875 !default;\n$font-size-lg:                $font-size-base * 1.25 !default;\n\n$font-weight-lighter:         lighter !default;\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-medium:          500 !default;\n$font-weight-semibold:        600 !default;\n$font-weight-bold:            700 !default;\n$font-weight-bolder:          bolder !default;\n\n$font-weight-base:            $font-weight-normal !default;\n\n$line-height-base:            1.5 !default;\n$line-height-sm:              1.25 !default;\n$line-height-lg:              2 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n// scss-docs-end font-variables\n\n// scss-docs-start font-sizes\n$font-sizes: (\n  1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size\n) !default;\n// scss-docs-end font-sizes\n\n// scss-docs-start headings-variables\n$headings-margin-bottom:      $spacer * .5 !default;\n$headings-font-family:        null !default;\n$headings-font-style:         null !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              null !default;\n// scss-docs-end headings-variables\n\n// scss-docs-start display-headings\n$display-font-sizes: (\n  1: 5rem,\n  2: 4.5rem,\n  3: 4rem,\n  4: 3.5rem,\n  5: 3rem,\n  6: 2.5rem\n) !default;\n\n$display-font-family: null !default;\n$display-font-style:  null !default;\n$display-font-weight: 300 !default;\n$display-line-height: $headings-line-height !default;\n// scss-docs-end display-headings\n\n// scss-docs-start type-variables\n$lead-font-size:              $font-size-base * 1.25 !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             .875em !default;\n\n$sub-sup-font-size:           .75em !default;\n\n$text-muted:                  var(--#{$prefix}secondary-color) !default;\n\n$initialism-font-size:        $small-font-size !default;\n\n$blockquote-margin-y:         $spacer !default;\n$blockquote-font-size:        $font-size-base * 1.25 !default;\n$blockquote-footer-color:     $gray-600 !default;\n$blockquote-footer-font-size: $small-font-size !default;\n\n$hr-margin-y:                 $spacer !default;\n$hr-color:                    inherit !default;\n\n// fusv-disable\n$hr-bg-color:                 null !default; // Deprecated in v5.2.0\n$hr-height:                   null !default; // Deprecated in v5.2.0\n// fusv-enable\n\n$hr-border-color:             null !default; // Allows for inherited colors\n$hr-border-width:             var(--#{$prefix}border-width) !default;\n$hr-opacity:                  .25 !default;\n\n$legend-margin-bottom:        .5rem !default;\n$legend-font-size:            1.5rem !default;\n$legend-font-weight:          null !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-padding:                .1875em !default;\n$mark-bg:                     $yellow-100 !default;\n// scss-docs-end type-variables\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y:        .5rem !default;\n$table-cell-padding-x:        .5rem !default;\n$table-cell-padding-y-sm:     .25rem !default;\n$table-cell-padding-x-sm:     .25rem !default;\n\n$table-cell-vertical-align:   top !default;\n\n$table-color:                 var(--#{$prefix}body-color) !default;\n$table-bg:                    transparent !default;\n$table-accent-bg:             transparent !default;\n\n$table-th-font-weight:        null !default;\n\n$table-striped-color:         $table-color !default;\n$table-striped-bg-factor:     .05 !default;\n$table-striped-bg:            rgba($black, $table-striped-bg-factor) !default;\n\n$table-active-color:          $table-color !default;\n$table-active-bg-factor:      .1 !default;\n$table-active-bg:             rgba($black, $table-active-bg-factor) !default;\n\n$table-hover-color:           $table-color !default;\n$table-hover-bg-factor:       .075 !default;\n$table-hover-bg:              rgba($black, $table-hover-bg-factor) !default;\n\n$table-border-factor:         .1 !default;\n$table-border-width:          var(--#{$prefix}border-width) !default;\n$table-border-color:          var(--#{$prefix}border-color) !default;\n\n$table-striped-order:         odd !default;\n$table-striped-columns-order: even !default;\n\n$table-group-separator-color: currentcolor !default;\n\n$table-caption-color:         $text-muted !default;\n\n$table-bg-scale:              -80% !default;\n// scss-docs-end table-variables\n\n// scss-docs-start table-loop\n$table-variants: (\n  \"primary\":    shift-color($primary, $table-bg-scale),\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\n  \"success\":    shift-color($success, $table-bg-scale),\n  \"info\":       shift-color($info, $table-bg-scale),\n  \"warning\":    shift-color($warning, $table-bg-scale),\n  \"danger\":     shift-color($danger, $table-bg-scale),\n  \"light\":      $light,\n  \"dark\":       $dark,\n) !default;\n// scss-docs-end table-loop\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n// scss-docs-start input-btn-variables\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-font-family:       null !default;\n$input-btn-font-size:         $font-size-base !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:         .25rem !default;\n$input-btn-focus-color-opacity: .25 !default;\n$input-btn-focus-color:         rgba($component-active-bg, $input-btn-focus-color-opacity) !default;\n$input-btn-focus-blur:          0 !default;\n$input-btn-focus-box-shadow:    0 0 $input-btn-focus-blur $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-font-size-sm:      $font-size-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-font-size-lg:      $font-size-lg !default;\n\n$input-btn-border-width:      var(--#{$prefix}border-width) !default;\n// scss-docs-end input-btn-variables\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n// scss-docs-start btn-variables\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-font-family:             $input-btn-font-family !default;\n$btn-font-size:               $input-btn-font-size !default;\n$btn-line-height:             $input-btn-line-height !default;\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\n$btn-focus-width:             $input-btn-focus-width !default;\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\n\n$btn-link-color:              var(--#{$prefix}link-color) !default;\n$btn-link-hover-color:        var(--#{$prefix}link-hover-color) !default;\n$btn-link-disabled-color:     $gray-600 !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$btn-hover-bg-shade-amount:       15% !default;\n$btn-hover-bg-tint-amount:        15% !default;\n$btn-hover-border-shade-amount:   20% !default;\n$btn-hover-border-tint-amount:    10% !default;\n$btn-active-bg-shade-amount:      20% !default;\n$btn-active-bg-tint-amount:       20% !default;\n$btn-active-border-shade-amount:  25% !default;\n$btn-active-border-tint-amount:   10% !default;\n// scss-docs-end btn-variables\n\n\n// Forms\n\n// scss-docs-start form-text-variables\n$form-text-margin-top:                  .25rem !default;\n$form-text-font-size:                   $small-font-size !default;\n$form-text-font-style:                  null !default;\n$form-text-font-weight:                 null !default;\n$form-text-color:                       $text-muted !default;\n// scss-docs-end form-text-variables\n\n// scss-docs-start form-label-variables\n$form-label-margin-bottom:              .5rem !default;\n$form-label-font-size:                  null !default;\n$form-label-font-style:                 null !default;\n$form-label-font-weight:                null !default;\n$form-label-color:                      null !default;\n// scss-docs-end form-label-variables\n\n// scss-docs-start form-input-variables\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-font-family:                     $input-btn-font-family !default;\n$input-font-size:                       $input-btn-font-size !default;\n$input-font-weight:                     $font-weight-base !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\n\n$input-bg:                              var(--#{$prefix}form-control-bg) !default;\n$input-disabled-color:                  null !default;\n$input-disabled-bg:                     var(--#{$prefix}form-control-disabled-bg) !default;\n$input-disabled-border-color:           null !default;\n\n$input-color:                           var(--#{$prefix}body-color) !default;\n$input-border-color:                    var(--#{$prefix}border-color) !default; //$gray-400\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      $box-shadow-inset !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              tint-color($component-active-bg, 50%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     $input-btn-focus-width !default;\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color:               var(--#{$prefix}secondary-color) !default;\n$input-plaintext-color:                 var(--#{$prefix}body-color) !default;\n\n$input-height-border:                   calc($input-border-width * 2) !default; // stylelint-disable-line function-disallowed-list\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y * .5) !default;\n\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\n$input-height-sm:                       add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\n$input-height-lg:                       add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-color-width:                      3rem !default;\n// scss-docs-end form-input-variables\n\n// scss-docs-start form-check-variables\n$form-check-input-width:                  1em !default;\n$form-check-min-height:                   $font-size-base * $line-height-base !default;\n$form-check-padding-start:                $form-check-input-width + .5em !default;\n$form-check-margin-bottom:                .125rem !default;\n$form-check-label-color:                  null !default;\n$form-check-label-cursor:                 null !default;\n$form-check-transition:                   null !default;\n\n$form-check-input-active-filter:          brightness(90%) !default;\n\n$form-check-input-bg:                     $input-bg !default;\n$form-check-input-border:                 var(--#{$prefix}border-width) solid var(--#{$prefix}border-color) !default;\n$form-check-input-border-radius:          .25em !default;\n$form-check-radio-border-radius:          50% !default;\n$form-check-input-focus-border:           $input-focus-border-color !default;\n$form-check-input-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n\n$form-check-input-checked-color:          $component-active-color !default;\n$form-check-input-checked-bg-color:       $component-active-bg !default;\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color !default;\n$form-check-input-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/></svg>\") !default;\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\n\n$form-check-input-indeterminate-color:          $component-active-color !default;\n$form-check-input-indeterminate-bg-color:       $component-active-bg !default;\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color !default;\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\") !default;\n\n$form-check-input-disabled-opacity:        .5 !default;\n$form-check-label-disabled-opacity:        $form-check-input-disabled-opacity !default;\n$form-check-btn-check-disabled-opacity:    $btn-disabled-opacity !default;\n\n$form-check-inline-margin-end:    1rem !default;\n// scss-docs-end form-check-variables\n\n// scss-docs-start form-switch-variables\n$form-switch-color:               rgba($black, .25) !default;\n$form-switch-width:               2em !default;\n$form-switch-padding-start:       $form-switch-width + .5em !default;\n$form-switch-bg-image:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\") !default;\n$form-switch-border-radius:       $form-switch-width !default;\n$form-switch-transition:          background-position .15s ease-in-out !default;\n\n$form-switch-focus-color:         $input-focus-border-color !default;\n$form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\") !default;\n\n$form-switch-checked-color:       $component-active-color !default;\n$form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\") !default;\n$form-switch-checked-bg-position: right center !default;\n// scss-docs-end form-switch-variables\n\n// scss-docs-start input-group-variables\n$input-group-addon-padding-y:           $input-padding-y !default;\n$input-group-addon-padding-x:           $input-padding-x !default;\n$input-group-addon-font-weight:         $input-font-weight !default;\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  var(--#{$prefix}tertiary-bg) !default;\n$input-group-addon-border-color:        $input-border-color !default;\n// scss-docs-end input-group-variables\n\n// scss-docs-start form-select-variables\n$form-select-padding-y:             $input-padding-y !default;\n$form-select-padding-x:             $input-padding-x !default;\n$form-select-font-family:           $input-font-family !default;\n$form-select-font-size:             $input-font-size !default;\n$form-select-indicator-padding:     $form-select-padding-x * 3 !default; // Extra padding for background-image\n$form-select-font-weight:           $input-font-weight !default;\n$form-select-line-height:           $input-line-height !default;\n$form-select-color:                 $input-color !default;\n$form-select-bg:                    $input-bg !default;\n$form-select-disabled-color:        null !default;\n$form-select-disabled-bg:           $input-disabled-bg !default;\n$form-select-disabled-border-color: $input-disabled-border-color !default;\n$form-select-bg-position:           right $form-select-padding-x center !default;\n$form-select-bg-size:               16px 12px !default; // In pixels because image dimensions\n$form-select-indicator-color:       $gray-800 !default;\n$form-select-indicator:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/></svg>\") !default;\n\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5 + $form-select-indicator-padding !default;\n$form-select-feedback-icon-position:    center right $form-select-indicator-padding !default;\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half !default;\n\n$form-select-border-width:        $input-border-width !default;\n$form-select-border-color:        $input-border-color !default;\n$form-select-border-radius:       $input-border-radius !default;\n$form-select-box-shadow:          $box-shadow-inset !default;\n\n$form-select-focus-border-color:  $input-focus-border-color !default;\n$form-select-focus-width:         $input-focus-width !default;\n$form-select-focus-box-shadow:    0 0 0 $form-select-focus-width $input-btn-focus-color !default;\n\n$form-select-padding-y-sm:        $input-padding-y-sm !default;\n$form-select-padding-x-sm:        $input-padding-x-sm !default;\n$form-select-font-size-sm:        $input-font-size-sm !default;\n$form-select-border-radius-sm:    $input-border-radius-sm !default;\n\n$form-select-padding-y-lg:        $input-padding-y-lg !default;\n$form-select-padding-x-lg:        $input-padding-x-lg !default;\n$form-select-font-size-lg:        $input-font-size-lg !default;\n$form-select-border-radius-lg:    $input-border-radius-lg !default;\n\n$form-select-transition:          $input-transition !default;\n// scss-docs-end form-select-variables\n\n// scss-docs-start form-range-variables\n$form-range-track-width:          100% !default;\n$form-range-track-height:         .5rem !default;\n$form-range-track-cursor:         pointer !default;\n$form-range-track-bg:             var(--#{$prefix}tertiary-bg) !default;\n$form-range-track-border-radius:  1rem !default;\n$form-range-track-box-shadow:     $box-shadow-inset !default;\n\n$form-range-thumb-width:                   1rem !default;\n$form-range-thumb-height:                  $form-range-thumb-width !default;\n$form-range-thumb-bg:                      $component-active-bg !default;\n$form-range-thumb-border:                  0 !default;\n$form-range-thumb-border-radius:           1rem !default;\n$form-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\n$form-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in Edge\n$form-range-thumb-active-bg:               tint-color($component-active-bg, 70%) !default;\n$form-range-thumb-disabled-bg:             var(--#{$prefix}secondary-color) !default;\n$form-range-thumb-transition:              background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n// scss-docs-end form-range-variables\n\n// scss-docs-start form-file-variables\n$form-file-button-color:          $input-color !default;\n$form-file-button-bg:             var(--#{$prefix}tertiary-bg) !default;\n$form-file-button-hover-bg:       var(--#{$prefix}secondary-bg) !default;\n// scss-docs-end form-file-variables\n\n// scss-docs-start form-floating-variables\n$form-floating-height:                  add(3.5rem, $input-height-border) !default;\n$form-floating-line-height:             1.25 !default;\n$form-floating-padding-x:               $input-padding-x !default;\n$form-floating-padding-y:               1rem !default;\n$form-floating-input-padding-t:         1.625rem !default;\n$form-floating-input-padding-b:         .625rem !default;\n$form-floating-label-height:            1.875em !default;\n$form-floating-label-opacity:           .65 !default;\n$form-floating-label-transform:         scale(.85) translateY(-.5rem) translateX(.15rem) !default;\n$form-floating-label-disabled-color:    $gray-600 !default;\n$form-floating-transition:              opacity .1s ease-in-out, transform .1s ease-in-out !default;\n// scss-docs-end form-floating-variables\n\n// Form validation\n\n// scss-docs-start form-feedback-variables\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $form-text-font-size !default;\n$form-feedback-font-style:          $form-text-font-style !default;\n$form-feedback-valid-color:         $success !default;\n$form-feedback-invalid-color:       $danger !default;\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\n// scss-docs-end form-feedback-variables\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\n  \"valid\": (\n    \"color\": var(--#{$prefix}success-text),\n    \"icon\": $form-feedback-icon-valid,\n    \"tooltip-color\": #fff,\n    \"tooltip-bg-color\": var(--#{$prefix}success),\n    \"focus-box-shadow\": 0 0 $input-btn-focus-blur $input-focus-width rgba(var(--#{$prefix}success-rgb), $input-btn-focus-color-opacity),\n    \"border-color\": var(--#{$prefix}success),\n  ),\n  \"invalid\": (\n    \"color\": var(--#{$prefix}danger-text),\n    \"icon\": $form-feedback-icon-invalid,\n    \"tooltip-color\": #fff,\n    \"tooltip-bg-color\": var(--#{$prefix}danger),\n    \"focus-box-shadow\": 0 0 $input-btn-focus-blur $input-focus-width rgba(var(--#{$prefix}danger-rgb), $input-btn-focus-color-opacity),\n    \"border-color\": var(--#{$prefix}danger),\n  )\n) !default;\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-offcanvas-backdrop:         1040 !default;\n$zindex-offcanvas:                  1045 !default;\n$zindex-modal-backdrop:             1050 !default;\n$zindex-modal:                      1055 !default;\n$zindex-popover:                    1070 !default;\n$zindex-tooltip:                    1080 !default;\n$zindex-toast:                      1090 !default;\n// scss-docs-end zindex-stack\n\n// scss-docs-start zindex-levels-map\n$zindex-levels: (\n  n1: -1,\n  0: 0,\n  1: 1,\n  2: 2,\n  3: 3\n) !default;\n// scss-docs-end zindex-levels-map\n\n\n// Navs\n\n// scss-docs-start nav-variables\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-font-size:                null !default;\n$nav-link-font-weight:              null !default;\n$nav-link-color:                    var(--#{$prefix}link-color) !default;\n$nav-link-hover-color:              var(--#{$prefix}link-hover-color) !default;\n$nav-link-transition:               color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out !default;\n$nav-link-disabled-color:           var(--#{$prefix}secondary-color) !default;\n\n$nav-tabs-border-color:             var(--#{$prefix}border-color) !default;\n$nav-tabs-border-width:             var(--#{$prefix}border-width) !default;\n$nav-tabs-border-radius:            var(--#{$prefix}border-radius) !default;\n$nav-tabs-link-hover-border-color:  var(--#{$prefix}secondary-bg) var(--#{$prefix}secondary-bg) $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        var(--#{$prefix}emphasis-color) !default;\n$nav-tabs-link-active-bg:           var(--#{$prefix}body-bg) !default;\n$nav-tabs-link-active-border-color: var(--#{$prefix}border-color) var(--#{$prefix}border-color) $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n// scss-docs-end nav-variables\n\n\n// Navbar\n\n// scss-docs-start navbar-variables\n$navbar-padding-y:                  $spacer * .5 !default;\n$navbar-padding-x:                  null !default;\n\n$navbar-nav-link-padding-x:         .5rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) * .5 !default;\n$navbar-brand-margin-end:           1rem !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n$navbar-toggler-focus-width:        $btn-focus-width !default;\n$navbar-toggler-transition:         box-shadow .15s ease-in-out !default;\n\n$navbar-light-color:                rgba(var(--#{$prefix}emphasis-color-rgb), .65) !default;\n$navbar-light-hover-color:          rgba(var(--#{$prefix}emphasis-color-rgb), .8) !default;\n$navbar-light-active-color:         rgba(var(--#{$prefix}emphasis-color-rgb), 1) !default;\n$navbar-light-disabled-color:       rgba(var(--#{$prefix}emphasis-color-rgb), .3) !default;\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{rgba($body-color, .75)}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-light-toggler-border-color: rgba(var(--#{$prefix}emphasis-color-rgb), .15) !default;\n$navbar-light-brand-color:          $navbar-light-active-color !default;\n$navbar-light-brand-hover-color:    $navbar-light-active-color !default;\n// scss-docs-end navbar-variables\n\n// scss-docs-start navbar-dark-variables\n$navbar-dark-color:                 rgba($white, .55) !default;\n$navbar-dark-hover-color:           rgba($white, .75) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n$navbar-dark-brand-color:           $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color:     $navbar-dark-active-color !default;\n// scss-docs-end navbar-dark-variables\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n// scss-docs-start dropdown-variables\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-x:                0 !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-font-size:                $font-size-base !default;\n$dropdown-color:                    var(--#{$prefix}body-color) !default;\n$dropdown-bg:                       var(--#{$prefix}body-bg) !default;\n$dropdown-border-color:             var(--#{$prefix}border-color-translucent) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             var(--#{$prefix}border-width) !default;\n$dropdown-inner-border-radius:      calc($dropdown-border-radius - $dropdown-border-width) !default; // stylelint-disable-line function-disallowed-list\n$dropdown-divider-bg:               $dropdown-border-color !default;\n$dropdown-divider-margin-y:         $spacer * .5 !default;\n$dropdown-box-shadow:               $box-shadow !default;\n\n$dropdown-link-color:               var(--#{$prefix}body-color) !default;\n$dropdown-link-hover-color:         $dropdown-link-color !default;\n$dropdown-link-hover-bg:            var(--#{$prefix}tertiary-bg) !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-500 !default;\n\n$dropdown-item-padding-y:           $spacer * .25 !default;\n$dropdown-item-padding-x:           $spacer !default;\n\n$dropdown-header-color:             $gray-600 !default;\n$dropdown-header-padding-x:         $dropdown-item-padding-x !default;\n$dropdown-header-padding-y:         $dropdown-padding-y !default;\n// fusv-disable\n$dropdown-header-padding:           $dropdown-header-padding-y $dropdown-header-padding-x !default; // Deprecated in v5.2.0\n// fusv-enable\n// scss-docs-end dropdown-variables\n\n// scss-docs-start dropdown-dark-variables\n$dropdown-dark-color:               $gray-300 !default;\n$dropdown-dark-bg:                  $gray-800 !default;\n$dropdown-dark-border-color:        $dropdown-border-color !default;\n$dropdown-dark-divider-bg:          $dropdown-divider-bg !default;\n$dropdown-dark-box-shadow:          null !default;\n$dropdown-dark-link-color:          $dropdown-dark-color !default;\n$dropdown-dark-link-hover-color:    $white !default;\n$dropdown-dark-link-hover-bg:       rgba($white, .15) !default;\n$dropdown-dark-link-active-color:   $dropdown-link-active-color !default;\n$dropdown-dark-link-active-bg:      $dropdown-link-active-bg !default;\n$dropdown-dark-link-disabled-color: $gray-500 !default;\n$dropdown-dark-header-color:        $gray-500 !default;\n// scss-docs-end dropdown-dark-variables\n\n\n// Pagination\n\n// scss-docs-start pagination-variables\n$pagination-padding-y:              .375rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n\n$pagination-font-size:              $font-size-base !default;\n\n$pagination-color:                  var(--#{$prefix}link-color) !default;\n$pagination-bg:                     var(--#{$prefix}body-bg) !default;\n$pagination-border-radius:          var(--#{$prefix}border-radius) !default;\n$pagination-border-width:           var(--#{$prefix}border-width) !default;\n$pagination-margin-start:           calc($pagination-border-width * -1) !default; // stylelint-disable-line function-disallowed-list\n$pagination-border-color:           var(--#{$prefix}border-color) !default;\n\n$pagination-focus-color:            var(--#{$prefix}link-hover-color) !default;\n$pagination-focus-bg:               var(--#{$prefix}secondary-bg) !default;\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n$pagination-focus-outline:          0 !default;\n\n$pagination-hover-color:            var(--#{$prefix}link-hover-color) !default;\n$pagination-hover-bg:               var(--#{$prefix}tertiary-bg) !default;\n$pagination-hover-border-color:     var(--#{$prefix}border-color) !default; // Todo in v6: remove this?\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $component-active-bg !default;\n\n$pagination-disabled-color:         var(--#{$prefix}secondary-color) !default;\n$pagination-disabled-bg:            var(--#{$prefix}secondary-bg) !default;\n$pagination-disabled-border-color:  var(--#{$prefix}border-color) !default;\n\n$pagination-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$pagination-border-radius-sm:       $border-radius-sm !default;\n$pagination-border-radius-lg:       $border-radius-lg !default;\n// scss-docs-end pagination-variables\n\n\n// Placeholders\n\n// scss-docs-start placeholders\n$placeholder-opacity-max:           .5 !default;\n$placeholder-opacity-min:           .2 !default;\n// scss-docs-end placeholders\n\n// Cards\n\n// scss-docs-start card-variables\n$card-spacer-y:                     $spacer !default;\n$card-spacer-x:                     $spacer !default;\n$card-title-spacer-y:               $spacer * .5 !default;\n$card-title-color:                  null !default;\n$card-subtitle-color:               null !default;\n$card-border-width:                 var(--#{$prefix}border-width) !default;\n$card-border-color:                 var(--#{$prefix}border-color-translucent) !default;\n$card-border-radius:                var(--#{$prefix}border-radius) !default;\n$card-box-shadow:                   null !default;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\n$card-cap-padding-y:                $card-spacer-y * .5 !default;\n$card-cap-padding-x:                $card-spacer-x !default;\n$card-cap-bg:                       rgba(var(--#{$prefix}body-color-rgb), .03) !default;\n$card-cap-color:                    null !default;\n$card-height:                       null !default;\n$card-color:                        null !default;\n$card-bg:                           var(--#{$prefix}body-bg) !default;\n$card-img-overlay-padding:          $spacer !default;\n$card-group-margin:                 $grid-gutter-width * .5 !default;\n// scss-docs-end card-variables\n\n// Accordion\n\n// scss-docs-start accordion-variables\n$accordion-padding-y:                     1rem !default;\n$accordion-padding-x:                     1.25rem !default;\n$accordion-color:                         var(--#{$prefix}body-color) !default; // Sass variable because of $accordion-button-icon\n$accordion-bg:                            var(--#{$prefix}body-bg) !default;\n$accordion-border-width:                  var(--#{$prefix}border-width) !default;\n$accordion-border-color:                  var(--#{$prefix}border-color) !default;\n$accordion-border-radius:                 var(--#{$prefix}border-radius) !default;\n$accordion-inner-border-radius:           subtract($accordion-border-radius, $accordion-border-width) !default;\n\n$accordion-body-padding-y:                $accordion-padding-y !default;\n$accordion-body-padding-x:                $accordion-padding-x !default;\n\n$accordion-button-padding-y:              $accordion-padding-y !default;\n$accordion-button-padding-x:              $accordion-padding-x !default;\n$accordion-button-color:                  var(--#{$prefix}body-color) !default;\n$accordion-button-bg:                     var(--#{$prefix}accordion-bg) !default;\n$accordion-transition:                    $btn-transition, border-radius .15s ease !default;\n$accordion-button-active-bg:              var(--#{$prefix}primary-bg-subtle) !default;\n$accordion-button-active-color:           var(--#{$prefix}primary-text) !default;\n\n$accordion-button-focus-border-color:     $input-focus-border-color !default;\n$accordion-button-focus-box-shadow:       $btn-focus-box-shadow !default;\n\n$accordion-icon-width:                    1.25rem !default;\n$accordion-icon-color:                    $body-color !default;\n$accordion-icon-active-color:             $primary-text !default;\n$accordion-icon-transition:               transform .2s ease-in-out !default;\n$accordion-icon-transform:                rotate(-180deg) !default;\n\n$accordion-button-icon:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n$accordion-button-active-icon:  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n// scss-docs-end accordion-variables\n\n// Tooltips\n\n// scss-docs-start tooltip-variables\n$tooltip-font-size:                 $font-size-sm !default;\n$tooltip-max-width:                 200px !default;\n$tooltip-color:                     var(--#{$prefix}body-bg) !default;\n$tooltip-bg:                        var(--#{$prefix}emphasis-color) !default;\n$tooltip-border-radius:             var(--#{$prefix}border-radius) !default;\n$tooltip-opacity:                   .9 !default;\n$tooltip-padding-y:                 $spacer * .25 !default;\n$tooltip-padding-x:                 $spacer * .5 !default;\n$tooltip-margin:                    null !default; // TODO: remove this in v6\n\n$tooltip-arrow-width:               .8rem !default;\n$tooltip-arrow-height:              .4rem !default;\n// fusv-disable\n$tooltip-arrow-color:               null !default; // Deprecated in Bootstrap 5.2.0 for CSS variables\n// fusv-enable\n// scss-docs-end tooltip-variables\n\n// Form tooltips must come after regular tooltips\n// scss-docs-start tooltip-feedback-variables\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   null !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n// scss-docs-end tooltip-feedback-variables\n\n\n// Popovers\n\n// scss-docs-start popover-variables\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        var(--#{$prefix}body-bg) !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              var(--#{$prefix}border-width) !default;\n$popover-border-color:              var(--#{$prefix}border-color-translucent) !default;\n$popover-border-radius:             var(--#{$prefix}border-radius-lg) !default;\n$popover-inner-border-radius:       calc($popover-border-radius - $popover-border-width) !default; // stylelint-disable-line function-disallowed-list\n$popover-box-shadow:                $box-shadow !default;\n\n$popover-header-font-size:          $font-size-base !default;\n$popover-header-bg:                 var(--#{$prefix}secondary-bg) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          $spacer !default;\n\n$popover-body-color:                var(--#{$prefix}body-color) !default;\n$popover-body-padding-y:            $spacer !default;\n$popover-body-padding-x:            $spacer !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n// scss-docs-end popover-variables\n\n// fusv-disable\n// Deprecated in Bootstrap 5.2.0 for CSS variables\n$popover-arrow-color:               $popover-bg !default;\n$popover-arrow-outer-color:         var(--#{$prefix}border-color-translucent) !default;\n// fusv-enable\n\n\n// Toasts\n\n// scss-docs-start toast-variables\n$toast-max-width:                   350px !default;\n$toast-padding-x:                   .75rem !default;\n$toast-padding-y:                   .5rem !default;\n$toast-font-size:                   .875rem !default;\n$toast-color:                       null !default;\n$toast-background-color:            rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\n$toast-border-width:                var(--#{$prefix}border-width) !default;\n$toast-border-color:                var(--#{$prefix}border-color-translucent) !default;\n$toast-border-radius:               var(--#{$prefix}border-radius) !default;\n$toast-box-shadow:                  var(--#{$prefix}box-shadow) !default;\n$toast-spacing:                     $container-padding-x !default;\n\n$toast-header-color:                var(--#{$prefix}secondary-color) !default;\n$toast-header-background-color:     rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\n$toast-header-border-color:         $toast-border-color !default;\n// scss-docs-end toast-variables\n\n\n// Badges\n\n// scss-docs-start badge-variables\n$badge-font-size:                   .75em !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-color:                       $white !default;\n$badge-padding-y:                   .35em !default;\n$badge-padding-x:                   .65em !default;\n$badge-border-radius:               $border-radius !default;\n// scss-docs-end badge-variables\n\n\n// Modals\n\n// scss-docs-start modal-variables\n$modal-inner-padding:               $spacer !default;\n\n$modal-footer-margin-between:       .5rem !default;\n\n$modal-dialog-margin:               .5rem !default;\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-color:               null !default;\n$modal-content-bg:                  var(--#{$prefix}body-bg) !default;\n$modal-content-border-color:        var(--#{$prefix}border-color-translucent) !default;\n$modal-content-border-width:        var(--#{$prefix}border-width) !default;\n$modal-content-border-radius:       var(--#{$prefix}border-radius-lg) !default;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\n$modal-content-box-shadow-xs:       $box-shadow-sm !default;\n$modal-content-box-shadow-sm-up:    $box-shadow !default;\n\n$modal-backdrop-bg:                 $black !default;\n$modal-backdrop-opacity:            .5 !default;\n\n$modal-header-border-color:         var(--#{$prefix}border-color) !default;\n$modal-header-border-width:         $modal-content-border-width !default;\n$modal-header-padding-y:            $modal-inner-padding !default;\n$modal-header-padding-x:            $modal-inner-padding !default;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-footer-bg:                   null !default;\n$modal-footer-border-color:         $modal-header-border-color !default;\n$modal-footer-border-width:         $modal-header-border-width !default;\n\n$modal-sm:                          300px !default;\n$modal-md:                          500px !default;\n$modal-lg:                          800px !default;\n$modal-xl:                          1140px !default;\n\n$modal-fade-transform:              translate(0, -50px) !default;\n$modal-show-transform:              none !default;\n$modal-transition:                  transform .3s ease-out !default;\n$modal-scale-transform:             scale(1.02) !default;\n// scss-docs-end modal-variables\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n// scss-docs-start alert-variables\n$alert-padding-y:               $spacer !default;\n$alert-padding-x:               $spacer !default;\n$alert-margin-bottom:           1rem !default;\n$alert-border-radius:           $border-radius !default;\n$alert-link-font-weight:        $font-weight-bold !default;\n$alert-border-width:            var(--#{$prefix}border-width) !default;\n$alert-bg-scale:                -80% !default;\n$alert-border-scale:            -70% !default;\n$alert-color-scale:             40% !default;\n$alert-dismissible-padding-r:   $alert-padding-x * 3 !default; // 3x covers width of x plus default padding on either side\n// scss-docs-end alert-variables\n\n// fusv-disable\n$alert-bg-scale:                -80% !default; // Deprecated in v5.2.0, to be removed in v6\n$alert-border-scale:            -70% !default; // Deprecated in v5.2.0, to be removed in v6\n$alert-color-scale:             40% !default; // Deprecated in v5.2.0, to be removed in v6\n// fusv-enable\n\n// Progress bars\n\n// scss-docs-start progress-variables\n$progress-height:                   1rem !default;\n$progress-font-size:                $font-size-base * .75 !default;\n$progress-bg:                       var(--#{$prefix}secondary-bg) !default;\n$progress-border-radius:            var(--#{$prefix}border-radius) !default;\n$progress-box-shadow:               var(--#{$prefix}box-shadow-inset) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   $primary !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n// scss-docs-end progress-variables\n\n\n// List group\n\n// scss-docs-start list-group-variables\n$list-group-color:                  var(--#{$prefix}body-color) !default;\n$list-group-bg:                     var(--#{$prefix}body-bg) !default;\n$list-group-border-color:           var(--#{$prefix}border-color) !default;\n$list-group-border-width:           var(--#{$prefix}border-width) !default;\n$list-group-border-radius:          var(--#{$prefix}border-radius) !default;\n\n$list-group-item-padding-y:         $spacer * .5 !default;\n$list-group-item-padding-x:         $spacer !default;\n// fusv-disable\n$list-group-item-bg-scale:          -80% !default; // Deprecated in v5.3.0\n$list-group-item-color-scale:       40% !default; // Deprecated in v5.3.0\n// fusv-enable\n\n$list-group-hover-bg:               var(--#{$prefix}tertiary-bg) !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         var(--#{$prefix}secondary-color) !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           var(--#{$prefix}secondary-color) !default;\n$list-group-action-hover-color:     var(--#{$prefix}emphasis-color) !default;\n\n$list-group-action-active-color:    var(--#{$prefix}body-color) !default;\n$list-group-action-active-bg:       var(--#{$prefix}secondary-bg) !default;\n// scss-docs-end list-group-variables\n\n\n// Image thumbnails\n\n// scss-docs-start thumbnail-variables\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      var(--#{$prefix}body-bg) !default;\n$thumbnail-border-width:            var(--#{$prefix}border-width) !default;\n$thumbnail-border-color:            var(--#{$prefix}border-color) !default;\n$thumbnail-border-radius:           var(--#{$prefix}border-radius) !default;\n$thumbnail-box-shadow:              var(--#{$prefix}box-shadow-sm) !default;\n// scss-docs-end thumbnail-variables\n\n\n// Figures\n\n// scss-docs-start figure-variables\n$figure-caption-font-size:          $small-font-size !default;\n$figure-caption-color:              var(--#{$prefix}secondary-color) !default;\n// scss-docs-end figure-variables\n\n\n// Breadcrumbs\n\n// scss-docs-start breadcrumb-variables\n$breadcrumb-font-size:              null !default;\n$breadcrumb-padding-y:              0 !default;\n$breadcrumb-padding-x:              0 !default;\n$breadcrumb-item-padding-x:         .5rem !default;\n$breadcrumb-margin-bottom:          1rem !default;\n$breadcrumb-bg:                     null !default;\n$breadcrumb-divider-color:          var(--#{$prefix}secondary-color) !default;\n$breadcrumb-active-color:           var(--#{$prefix}secondary-color) !default;\n$breadcrumb-divider:                quote(\"/\") !default;\n$breadcrumb-divider-flipped:        $breadcrumb-divider !default;\n$breadcrumb-border-radius:          null !default;\n// scss-docs-end breadcrumb-variables\n\n// Carousel\n\n// scss-docs-start carousel-variables\n$carousel-control-color:             $white !default;\n$carousel-control-width:             15% !default;\n$carousel-control-opacity:           .5 !default;\n$carousel-control-hover-opacity:     .9 !default;\n$carousel-control-transition:        opacity .15s ease !default;\n\n$carousel-indicator-width:           30px !default;\n$carousel-indicator-height:          3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer:          3px !default;\n$carousel-indicator-opacity:         .5 !default;\n$carousel-indicator-active-bg:       $white !default;\n$carousel-indicator-active-opacity:  1 !default;\n$carousel-indicator-transition:      opacity .6s ease !default;\n\n$carousel-caption-width:             70% !default;\n$carousel-caption-color:             $white !default;\n$carousel-caption-padding-y:         1.25rem !default;\n$carousel-caption-spacer:            1.25rem !default;\n\n$carousel-control-icon-width:        2rem !default;\n\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\") !default;\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n\n$carousel-transition-duration:       .6s !default;\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n// scss-docs-end carousel-variables\n\n// scss-docs-start carousel-dark-variables\n$carousel-dark-indicator-active-bg:  $black !default;\n$carousel-dark-caption-color:        $black !default;\n$carousel-dark-control-icon-filter:  invert(1) grayscale(100) !default;\n// scss-docs-end carousel-dark-variables\n\n\n// Spinners\n\n// scss-docs-start spinner-variables\n$spinner-width:           2rem !default;\n$spinner-height:          $spinner-width !default;\n$spinner-vertical-align:  -.125em !default;\n$spinner-border-width:    .25em !default;\n$spinner-animation-speed: .75s !default;\n\n$spinner-width-sm:        1rem !default;\n$spinner-height-sm:       $spinner-width-sm !default;\n$spinner-border-width-sm: .2em !default;\n// scss-docs-end spinner-variables\n\n\n// Close\n\n// scss-docs-start close-variables\n$btn-close-width:            1em !default;\n$btn-close-height:           $btn-close-width !default;\n$btn-close-padding-x:        .25em !default;\n$btn-close-padding-y:        $btn-close-padding-x !default;\n$btn-close-color:            $black !default;\n$btn-close-bg:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/></svg>\") !default;\n$btn-close-focus-shadow:     $input-btn-focus-box-shadow !default;\n$btn-close-opacity:          .5 !default;\n$btn-close-hover-opacity:    .75 !default;\n$btn-close-focus-opacity:    1 !default;\n$btn-close-disabled-opacity: .25 !default;\n$btn-close-white-filter:     invert(1) grayscale(100%) brightness(200%) !default;\n// scss-docs-end close-variables\n\n\n// Offcanvas\n\n// scss-docs-start offcanvas-variables\n$offcanvas-padding-y:               $modal-inner-padding !default;\n$offcanvas-padding-x:               $modal-inner-padding !default;\n$offcanvas-horizontal-width:        400px !default;\n$offcanvas-vertical-height:         30vh !default;\n$offcanvas-transition-duration:     .3s !default;\n$offcanvas-border-color:            $modal-content-border-color !default;\n$offcanvas-border-width:            $modal-content-border-width !default;\n$offcanvas-title-line-height:       $modal-title-line-height !default;\n$offcanvas-bg-color:                var(--#{$prefix}body-bg) !default;\n$offcanvas-color:                   var(--#{$prefix}body-color) !default;\n$offcanvas-box-shadow:              $modal-content-box-shadow-xs !default;\n$offcanvas-backdrop-bg:             $modal-backdrop-bg !default;\n$offcanvas-backdrop-opacity:        $modal-backdrop-opacity !default;\n// scss-docs-end offcanvas-variables\n\n// Code\n\n$code-font-size:                    $small-font-size !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .1875rem !default;\n$kbd-padding-x:                     .375rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         var(--#{$prefix}body-bg) !default;\n$kbd-bg:                            var(--#{$prefix}body-color) !default;\n$nested-kbd-font-weight:            null !default; // Deprecated in v5.2.0, removing in v6\n\n$pre-color:                         null !default;\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl xxl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// scss-docs-start stacks\n.hstack {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  align-self: stretch;\n}\n\n.vstack {\n  display: flex;\n  flex: 1 1 auto;\n  flex-direction: column;\n  align-self: stretch;\n}\n// scss-docs-end stacks\n", "//\n// Visually hidden\n//\n\n.visually-hidden,\n.visually-hidden-focusable:not(:focus):not(:focus-within) {\n  @include visually-hidden();\n}\n", "// stylelint-disable declaration-no-important\n\n// Hide content visually while keeping it accessible to assistive technologies\n//\n// See: https://www.a11yproject.com/posts/2013-01-11-how-to-hide-content/\n// See: https://kittygiraudel.com/2016/10/13/css-hide-and-seek/\n\n@mixin visually-hidden() {\n  position: absolute !important;\n  width: 1px !important;\n  height: 1px !important;\n  padding: 0 !important;\n  margin: -1px !important; // Fix for https://github.com/twbs/bootstrap/issues/25686\n  overflow: hidden !important;\n  clip: rect(0, 0, 0, 0) !important;\n  white-space: nowrap !important;\n  border: 0 !important;\n}\n\n// Use to only display content when it's focused, or one of its child elements is focused\n// (i.e. when focus is within the element/container that the class was applied to)\n//\n// Useful for \"Skip to main content\" links; see https://www.w3.org/TR/2013/NOTE-WCAG20-TECHS-20130905/G1\n\n@mixin visually-hidden-focusable() {\n  &:not(:focus):not(:focus-within) {\n    @include visually-hidden();\n  }\n}\n", "//\n// Stretched link\n//\n\n.stretched-link {\n  &::#{$stretched-link-pseudo-element} {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    z-index: $stretched-link-z-index;\n    content: \"\";\n  }\n}\n", "//\n// Text truncation\n//\n\n.text-truncate {\n  @include text-truncate();\n}\n", "// Text truncate\n// Requires inline-block or block for proper styling\n\n@mixin text-truncate() {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n", ".vr {\n  display: inline-block;\n  align-self: stretch;\n  width: 1px;\n  min-height: 1em;\n  background-color: currentcolor;\n  opacity: $hr-opacity;\n}\n", "// Utility generator\n// Used to generate utilities & print utilities\n@mixin generate-utility($utility, $infix, $is-rfs-media-query: false) {\n  $values: map-get($utility, values);\n\n  // If the values are a list or string, convert it into a map\n  @if type-of($values) == \"string\" or type-of(nth($values, 1)) != \"list\" {\n    $values: zip($values, $values);\n  }\n\n  @each $key, $value in $values {\n    $properties: map-get($utility, property);\n\n    // Multiple properties are possible, for example with vertical or horizontal margins or paddings\n    @if type-of($properties) == \"string\" {\n      $properties: append((), $properties);\n    }\n\n    // Use custom class if present\n    $property-class: if(map-has-key($utility, class), map-get($utility, class), nth($properties, 1));\n    $property-class: if($property-class == null, \"\", $property-class);\n\n    // Use custom CSS variable name if present, otherwise default to `class`\n    $css-variable-name: if(map-has-key($utility, css-variable-name), map-get($utility, css-variable-name), map-get($utility, class));\n\n    // State params to generate pseudo-classes\n    $state: if(map-has-key($utility, state), map-get($utility, state), ());\n\n    $infix: if($property-class == \"\" and str-slice($infix, 1, 1) == \"-\", str-slice($infix, 2), $infix);\n\n    // Don't prefix if value key is null (e.g. with shadow class)\n    $property-class-modifier: if($key, if($property-class == \"\" and $infix == \"\", \"\", \"-\") + $key, \"\");\n\n    @if map-get($utility, rfs) {\n      // Inside the media query\n      @if $is-rfs-media-query {\n        $val: rfs-value($value);\n\n        // Do not render anything if fluid and non fluid values are the same\n        $value: if($val == rfs-fluid-value($value), null, $val);\n      }\n      @else {\n        $value: rfs-fluid-value($value);\n      }\n    }\n\n    $is-css-var: map-get($utility, css-var);\n    $is-local-vars: map-get($utility, local-vars);\n    $is-rtl: map-get($utility, rtl);\n\n    @if $value != null {\n      @if $is-rtl == false {\n        /* rtl:begin:remove */\n      }\n\n      @if $is-css-var {\n        .#{$property-class + $infix + $property-class-modifier} {\n          --#{$prefix}#{$css-variable-name}: #{$value};\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            --#{$prefix}#{$css-variable-name}: #{$value};\n          }\n        }\n      } @else {\n        .#{$property-class + $infix + $property-class-modifier} {\n          @each $property in $properties {\n            @if $is-local-vars {\n              @each $local-var, $variable in $is-local-vars {\n                --#{$prefix}#{$local-var}: #{$variable};\n              }\n            }\n            #{$property}: $value if($enable-important-utilities, !important, null);\n          }\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            @each $property in $properties {\n              @if $is-local-vars {\n                @each $local-var, $variable in $is-local-vars {\n                  --#{$prefix}#{$local-var}: #{$variable};\n                }\n              }\n              #{$property}: $value if($enable-important-utilities, !important, null);\n            }\n          }\n        }\n      }\n\n      @if $is-rtl == false {\n        /* rtl:end:remove */\n      }\n    }\n  }\n}\n", "// Loop over each breakpoint\n@each $breakpoint in map-keys($grid-breakpoints) {\n\n  // Generate media query if needed\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    // Loop over each utility property\n    @each $key, $utility in $utilities {\n      // The utility can be disabled with `false`, thus check if the utility is a map first\n      // Only proceed if responsive media queries are enabled or if it's the base media query\n      @if type-of($utility) == \"map\" and (map-get($utility, responsive) or $infix == \"\") {\n        @include generate-utility($utility, $infix);\n      }\n    }\n  }\n}\n\n// RFS rescaling\n@media (min-width: $rfs-mq-value) {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @if (map-get($grid-breakpoints, $breakpoint) < $rfs-breakpoint) {\n      // Loop over each utility property\n      @each $key, $utility in $utilities {\n        // The utility can be disabled with `false`, thus check if the utility is a map first\n        // Only proceed if responsive media queries are enabled or if it's the base media query\n        @if type-of($utility) == \"map\" and map-get($utility, rfs) and (map-get($utility, responsive) or $infix == \"\") {\n          @include generate-utility($utility, $infix, true);\n        }\n      }\n    }\n  }\n}\n\n\n// Print utilities\n@media print {\n  @each $key, $utility in $utilities {\n    // The utility can be disabled with `false`, thus check if the utility is a map first\n    // Then check if the utility needs print styles\n    @if type-of($utility) == \"map\" and map-get($utility, print) == true {\n      @include generate-utility($utility, \"-print\");\n    }\n  }\n}\n"]}