{% extends 'newapp/content.html' %}

{% block title %}
编辑用户
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <div class="page-header">
                <h2>
                    <span class="glyphicon glyphicon-edit" aria-hidden="true"></span>
                    编辑用户
                    <small>修改用户信息</small>
                </h2>
            </div>

            <div class="panel panel-info">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <span class="glyphicon glyphicon-user" aria-hidden="true"></span>
                        用户信息编辑
                    </h3>
                </div>
                <div class="panel-body">
                    {% if error %}
                        <div class="alert alert-danger">
                            <span class="glyphicon glyphicon-exclamation-sign"></span>
                            {{ error }}
                        </div>
                    {% endif %}
                    
                    <form method="POST" action="/new/depart/user/edit/">
                        {% csrf_token %}
                        <input type="hidden" name="id" value="{{ user_info.id }}">
                        
                        <div class="form-group">
                            <label for="name">
                                <span class="glyphicon glyphicon-user"></span>
                                用户名
                            </label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ user_info.name }}" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="password">
                                <span class="glyphicon glyphicon-lock"></span>
                                密码
                            </label>
                            <input type="password" class="form-control" id="password" name="password" value="{{ user_info.password }}" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="age">
                                <span class="glyphicon glyphicon-time"></span>
                                年龄
                            </label>
                            <input type="number" class="form-control" id="age" name="age" value="{{ user_info.age }}" min="1" max="120" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="account">
                                <span class="glyphicon glyphicon-yen"></span>
                                账户余额
                            </label>
                            <input type="number" step="0.01" class="form-control" id="account" name="account" value="{{ user_info.account }}" min="0">
                        </div>
                        
                        <div class="form-group">
                            <label for="create_time">
                                <span class="glyphicon glyphicon-calendar"></span>
                                入职时间
                            </label>
                            <input type="date" class="form-control" id="create_time" name="create_time" value="{{ user_info.create_time|date:'Y-m-d' }}" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="depart_id">
                                <span class="glyphicon glyphicon-briefcase"></span>
                                部门
                            </label>
                            <select class="form-control" id="depart_id" name="depart_id" required>
                                <option value="">请选择部门</option>
                                {% for department in departments %}
                                    <option value="{{ department.id }}" {% if department.id == user_info.depart.id %}selected{% endif %}>
                                        {{ department.title }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="form-group text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <span class="glyphicon glyphicon-floppy-disk"></span>
                                保存修改
                            </button>
                            <a href="/new/depart/user/list/" class="btn btn-default btn-lg" style="margin-left: 10px;">
                                <span class="glyphicon glyphicon-arrow-left"></span>
                                返回列表
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-group label {
    font-weight: bold;
    color: #337ab7;
}
.page-header {
    border-bottom: 2px solid #5bc0de;
    padding-bottom: 15px;
    margin-bottom: 30px;
}
.panel-info > .panel-heading {
    background-color: #5bc0de;
    border-color: #46b8da;
}
</style>

{% endblock %}
