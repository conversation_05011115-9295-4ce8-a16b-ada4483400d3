"""
URL configuration for djangostudy project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    # 管理后台
    path("admin/", admin.site.urls),
    
    # 包含ljjapp的所有URL配置
    path('ljj/', include('ljjapp.urls')),
    
    # 包含newapp的所有URL配置  
    path('new/', include('newapp.urls')),
    
    # 为了保持向后兼容，也可以直接在根路径包含ljjapp的URLs
    # 这样原来的URL路径仍然可以访问
    path('', include('ljjapp.urls')),
]
