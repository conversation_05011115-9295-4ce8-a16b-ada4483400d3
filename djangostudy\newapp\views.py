from django.shortcuts import render, HttpResponse,redirect
from .models import Department,UserInfo
# Create your views here.

def home(request):
    return HttpResponse("欢迎来到NewApp首页")

def about(request):
    return HttpResponse("这是NewApp的关于页面")

def contact(request):
    return render(request, 'newapp/contact.html')

def products(request):
    products_list = [
        {"name": "产品A", "price": 100},
        {"name": "产品B", "price": 200},
        {"name": "产品C", "price": 300},
    ]
    return render(request, 'newapp/products.html', {'products': products_list})


def depart_list(request):
    if request.method == "GET":
        #获取数据库中所有数据
        data_list=Department.objects.all()
        print(data_list)
        return render(request,"newapp/depart_list.html",{"data_list":data_list})
    # title=request.POST.get("title")
    # Department.objects.create(title=title)
    return render(request,"newapp/depart_list.html")



def depart_add(request):
    if request.method == "GET":
        return render(request,"newapp/depart_add.html")
    department_name=request.POST.get("department_name")
    Department.objects.create(title=department_name)
    return redirect("/new/depart/list/")


def delete_depart(request):
    title=request.GET.get("title")
    Department.objects.filter(title=title).delete()
    return redirect("/new/depart/list/")

def edit_depart(request):
    if request.method =="GET":
        id=request.GET.get("id")
        title=request.GET.get("title")
        return render(request,"newapp/depart_edit.html",{"id":id,"title":title})
    new_title=request.POST.get("department_name")
    id=request.GET.get("id")
    Department.objects.filter(id=id).update(title=new_title)
    return redirect("/new/depart/list/")


def depart_userlist(request):
    if request.method == "GET":
        data_list=UserInfo.objects.all()
        print(data_list)
        return render(request,"newapp/depart_userlist.html",{"data_list":data_list})
    
def depart_user_add(request):
    if request.method=="GET":
        # 获取所有部门用于下拉选择
        departments = Department.objects.all()
        return render(request,"newapp/depart_user_add.html", {"departments": departments})
    name=request.POST.get("name")
    password=request.POST.get("password")
    age=request.POST.get("age")
    account=request.POST.get("account")
    create_time=request.POST.get("create_time")
    depart_id=request.POST.get("depart_id")
    
    # 获取部门对象
    try:
        department = Department.objects.get(id=depart_id)
        UserInfo.objects.create(name=name,password=password,age=age,account=account,create_time=create_time,depart=department.title)
        return redirect("/new/depart/user/list/")
    except Department.DoesNotExist:
        # 如果部门不存在，重新获取部门列表并显示错误
        departments = Department.objects.all()
        return render(request,"newapp/depart_user_add.html", {"error": "部门不存在", "departments": departments})
    except Exception as e:
        # 处理其他可能的错误
        departments = Department.objects.all()
        return render(request,"newapp/depart_user_add.html", {"error": f"添加用户失败: {str(e)}", "departments": departments})

def depart_user_edit(request):
    if request.method == "GET":
        user_id = request.GET.get("id")
        try:
            user_info = UserInfo.objects.get(id=user_id)
            departments = Department.objects.all()
            return render(request, "newapp/depart_user_edit.html", {
                "user_info": user_info,
                "departments": departments
            })
        except UserInfo.DoesNotExist:
            return redirect("/new/depart/user/list/")
    
    # POST请求 - 处理编辑
    user_id = request.POST.get("id")
    name = request.POST.get("name")
    password = request.POST.get("password")
    age = request.POST.get("age")
    account = request.POST.get("account")
    create_time = request.POST.get("create_time")
    depart_id = request.POST.get("depart_id")
    
    try:
        user_info = UserInfo.objects.get(id=user_id)
        department = Department.objects.get(id=depart_id)
        
        # 更新用户信息
        user_info.name = name
        user_info.password = password
        user_info.age = age
        user_info.account = account
        user_info.create_time = create_time
        user_info.depart = department
        user_info.save()
        
        return redirect("/new/depart/user/list/")
    except (UserInfo.DoesNotExist, Department.DoesNotExist) as e:
        departments = Department.objects.all()
        try:
            user_info = UserInfo.objects.get(id=user_id)
        except UserInfo.DoesNotExist:
            return redirect("/new/depart/user/list/")
        return render(request, "newapp/depart_user_edit.html", {
            "error": "用户或部门不存在",
            "user_info": user_info,
            "departments": departments
        })
    except Exception as e:
        departments = Department.objects.all()
        try:
            user_info = UserInfo.objects.get(id=user_id)
        except UserInfo.DoesNotExist:
            return redirect("/new/depart/user/list/")
        return render(request, "newapp/depart_user_edit.html", {
            "error": f"更新用户失败: {str(e)}",
            "user_info": user_info,
            "departments": departments
        })

def depart_user_delete(request):
    user_id = request.GET.get("id")
    try:
        UserInfo.objects.filter(id=user_id).delete()
    except Exception as e:
        print(f"删除用户失败: {str(e)}")
    return redirect("/new/depart/user/list/")

from django import forms
class UserModelForm(forms.ModelForm):
    class Meta:
        model=UserInfo
        fields=["name","password","age","account","create_time","depart"]
        widgets = {
    "name": forms.TextInput(attrs={"class": "form-control", "placeholder": "请输入姓名"}),
    "password": forms.PasswordInput(attrs={"class": "form-control", "placeholder": "请输入密码"}),
    "age": forms.NumberInput(attrs={"class": "form-control", "placeholder": "请输入年龄"}),
    "account": forms.NumberInput(attrs={"class": "form-control", "placeholder": "请输入余额"}),
    "create_time": forms.DateInput(attrs={"class": "form-control", "placeholder": "选择创建时间"}),
    "depart": forms.Select(attrs={"class": "form-control", "placeholder": "选择部门"}),
}
    

def depart_user_model_add(request):
    if request.method == "GET":
        form = UserModelForm()
        return render(request,"newapp/depart_user_model_add.html",{"form":form})
    form = UserModelForm(data=request.POST)
    if form.is_valid():
        form.save()
        return redirect("/new/depart/user/list/")
    
    return render(request,"newapp/depart_user_model_add.html",{"form":form})
