import pymysql

#1.链接myql

conn=pymysql.connect(host='127.0.0.1',port=3306,user='root',password='qw069174',charset='utf8',db='unicom')
#接收数据
cursor=conn.cursor(cursor=pymysql.cursors.DictCursor)

sql="update admin set username=%s where id=%d"
n1=input("请输入新的用户名:")
n2=int((input("请输入要修改的用户ID:")))
cursor.execute(sql,[n1,n2])

data_list=cursor.fetchall()
print(data_list)


conn.commit()





#3.关闭连接

cursor.close()
conn.close()

print(cursor)