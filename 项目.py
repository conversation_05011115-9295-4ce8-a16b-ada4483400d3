from flask import Flask, request, jsonify,render_template

import pymysql

app = Flask(__name__)


def connect_db():
    import pymysql

    #1.链接myql

    conn=pymysql.connect(host='127.0.0.1',port=3306,user='root',password='qw069174',charset='utf8',db='unicom')
    #接收数据
    cursor=conn.cursor(cursor=pymysql.cursors.DictCursor)
    return conn, cursor



@app.route('/add_user')
def add_user():
    return render_template('add_user.html')    

@app.route('/add/user', methods=['POST'])
def add_user_post():
    print(request.form)
    username = request.form.get('username')
    password = request.form.get('password')
    mobile = request.form.get('mobile')
    conn,cursor= connect_db()
    print(conn, cursor)
    print(username, password, mobile)
    # #2.执行sql语句
    sql = "INSERT INTO admin (username, password ,mobile) VALUES (%s, %s, %s)"
    new_sql="select * from admin "
    cursor.execute(sql, (username, password, mobile))
    conn.commit()
    cursor.execute(new_sql)
    data_list = cursor.fetchall()
    print(data_list)
    cursor.close()
    conn.close()

    # 这里可以添加数据库操作代码，将用户信息存储到数据库中
    # 例如使用pymysql连接数据库并执行插入操作
    
    # 假设插入成功，返回成功页面
    return render_template('成功页面.html')

@app.route('/show_users', methods=['GET'])
def show_users():
    conn,cursor=connect_db()
    print(conn, cursor)
    
    sql='select * from admin'
    cursor.execute(sql)
    data_list=cursor.fetchall()
    print(data_list)
    cursor.close()
    conn.close()
    return render_template('展示数据.html', users=data_list)

if __name__ == '__main__':
    app.run()
