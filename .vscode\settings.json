{"terminal.integrated.defaultProfile.windows": "Command Prompt", "terminal.integrated.shellIntegration.enabled": true, "terminal.integrated.shellIntegration.showWelcome": true, "terminal.integrated.shellIntegration.decorationsEnabled": "both", "terminal.integrated.enableMultiLinePasteWarning": "auto", "terminal.integrated.profiles.windows": {"PowerShell": {"source": "PowerShell", "icon": "terminal-powershell"}, "Command Prompt": {"path": ["${env:windir}\\Sysnative\\cmd.exe", "${env:windir}\\System32\\cmd.exe"], "args": [], "icon": "terminal-cmd"}, "PowerShell Core": {"path": "C:\\Program Files\\PowerShell\\7\\pwsh.exe", "icon": "terminal-powershell"}}, "terminal.integrated.automationProfile.windows": {"path": "${env:windir}\\System32\\cmd.exe"}, "commentTranslate.source": "intellsmi.comment-translate-ali.cloud", "commentTranslate.targetLanguage": "zh-CN", "workbench.colorTheme": "Quiet Light", "workbench.activityBar.iconClickBehavior": "toggle", "editor.fontSize": 18, "workbench.preferredDarkColorTheme": "Default Dark Modern", "workbench.editor.decorations.colors": false}