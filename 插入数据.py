import pymysql

#1.链接myql

conn=pymysql.connect(host='127.0.0.1',port=3306,user='root',password='qw069174',charset='utf8',db='unicom')
#接收数据
cursor=conn.cursor(cursor=pymysql.cursors.DictCursor)

#2.执行sql语句
# cursor.execute('insert into admin (username,password,mobile) values ("张三","123456","12345678901")')
# sql="insert into admin (username,password,mobile) values (%s,%s,%s)"
# cursor.execute(sql,("李四","123456","12345678902"))
while True:
    
    username = input("请输入用户名:")
    password = input("请输入密码:")
    mobile = input("请输入手机号:")
    sql="insert into admin (username,password,mobile) values (%s,%s,%s)"
    cursor.execute(sql,[username,password,mobile])
    conn.commit()
    if input("是否继续添加？(y/n)") != 'y':
        break

#3.关闭连接

cursor.close()
conn.close()

print(cursor)