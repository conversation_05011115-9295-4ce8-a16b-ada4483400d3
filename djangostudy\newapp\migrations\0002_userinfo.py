# Generated by Django 4.2.23 on 2025-07-21 07:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("newapp", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        primary_key=True, serialize=False, verbose_name="ID"
                    ),
                ),
                ("name", models.CharField(max_length=32, verbose_name="用户名")),
                ("password", models.<PERSON>r<PERSON><PERSON>(max_length=64, verbose_name="密码")),
                ("age", models.IntegerField(verbose_name="年龄")),
                (
                    "account",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name="账户余额",
                    ),
                ),
                ("create_time", models.DateField(verbose_name="入职时间")),
                (
                    "depart",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="newapp.department",
                    ),
                ),
            ],
        ),
    ]
