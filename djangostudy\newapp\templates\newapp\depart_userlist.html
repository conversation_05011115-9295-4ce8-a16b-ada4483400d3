{% extends 'newapp/content.html' %}

{% block title %}
用户管理
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="page-header">
                <h2>
                    <span class="glyphicon glyphicon-user" aria-hidden="true"></span>
                    用户管理系统
                    <small>管理所有用户信息</small>
                </h2>
            </div>
            
            <div style="margin-bottom: 20px">
                <a class="btn btn-success btn-lg" href="/new/depart/user/add/" role="button">
                    <span class="glyphicon glyphicon-plus" aria-hidden="true"></span>
                    新建用户
                </a>
                <a class="btn btn-success btn-lg" href="/new/depart/user/model/add/" role="button">
                    <span class="glyphicon glyphicon-plus" aria-hidden="true"></span>
                    新建modelform用户
                </a>
            </div>

            <div class="panel panel-primary">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <span class="glyphicon glyphicon-th-list" aria-hidden="true"></span>
                        用户列表 (共 {{ data_list|length }} 人)
                    </h3>
                </div>
                <div class="panel-body" style="padding: 0;">
                    {% if data_list %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover table-bordered">
                            <thead class="thead-dark" style="background-color: #337ab7; color: white;">
                                <tr>
                                    <th class="text-center" style="width: 60px;">ID</th>
                                    <th class="text-center" style="width: 120px;">用户名</th>
                                    <th class="text-center" style="width: 80px;">年龄</th>
                                    <th class="text-center" style="width: 120px;">账户余额</th>
                                    <th class="text-center" style="width: 120px;">入职时间</th>
                                    <th class="text-center" style="width: 100px;">部门</th>
                                    <th class="text-center" style="width: 150px;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in data_list %}
                                <tr>
                                    <td class="text-center">{{ item.id }}</td>
                                    <td class="text-center">
                                        <strong>{{ item.name }}</strong>
                                    </td>
                                    <td class="text-center">{{ item.age }}岁</td>
                                    <td class="text-center">
                                        <span class="text-success">
                                            <strong>¥{{ item.account }}</strong>
                                        </span>
                                    </td>
                                    <td class="text-center">{{ item.create_time }}</td>
                                    <td class="text-center">
                                        <span class="label label-info">{{ item.depart.title }}</span>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="/new/depart/user/edit/?id={{ item.id }}" class="btn btn-warning btn-sm" title="编辑用户">
                                                <span class="glyphicon glyphicon-edit" aria-hidden="true"></span>
                                                编辑
                                            </a>
                                            <a href="javascript:void(0);" class="btn btn-danger btn-sm delete-user-btn" 
                                               data-user-id="{{ item.id }}" data-user-name="{{ item.name }}" title="删除用户">
                                                <span class="glyphicon glyphicon-trash" aria-hidden="true"></span>
                                                删除
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center" style="padding: 50px;">
                        <span class="glyphicon glyphicon-info-sign" style="font-size: 48px; color: #ccc;"></span>
                        <h4 style="color: #999; margin-top: 20px;">暂无用户数据</h4>
                        <p style="color: #999;">点击上方"新建用户"按钮添加第一个用户</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">
                    <span class="glyphicon glyphicon-warning-sign text-danger"></span>
                    确认删除
                </h4>
            </div>
            <div class="modal-body">
                <p>您确定要删除用户 <strong id="deleteUserName"></strong> 吗？</p>
                <p class="text-danger">
                    <small>
                        <span class="glyphicon glyphicon-exclamation-sign"></span>
                        此操作不可撤销，请谨慎操作！
                    </small>
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <a id="confirmDeleteBtn" href="#" class="btn btn-danger">
                    <span class="glyphicon glyphicon-trash"></span>
                    确认删除
                </a>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 为删除按钮绑定点击事件
    $('.delete-user-btn').click(function() {
        var userId = $(this).data('user-id');
        var userName = $(this).data('user-name');
        
        document.getElementById('deleteUserName').textContent = userName;
        document.getElementById('confirmDeleteBtn').href = '/new/depart/user/delete/?id=' + userId;
        $('#deleteModal').modal('show');
    });
});

function deleteUser(userId, userName) {
    document.getElementById('deleteUserName').textContent = userName;
    document.getElementById('confirmDeleteBtn').href = '/new/depart/user/delete/?id=' + userId;
    $('#deleteModal').modal('show');
}
</script>

<style>
.table > thead > tr > th {
    border-bottom: 2px solid #337ab7;
}
.table-striped > tbody > tr:nth-of-type(odd) {
    background-color: #f9f9f9;
}
.table-hover > tbody > tr:hover {
    background-color: #f0f8ff;
}
.btn-group .btn {
    margin-right: 5px;
}
.btn-group .btn:last-child {
    margin-right: 0;
}
.page-header {
    border-bottom: 2px solid #337ab7;
    padding-bottom: 15px;
    margin-bottom: 30px;
}
</style>

{% endblock %}
